import { createRouter, createWebHistory, createWebHashHistory} from 'vue-router'
import Home from '../views/Home.vue'
import Layout from '../components/layout/Layout.vue'
import { useWallpaperStore } from '../stores/wallpaper.js'

// 路由配置
const routes = [
  {
    path: '/',
    name: 'Layout',
    component: Layout,
    meta: { requiresAuth: false },
    children:[
      {
        path: '',  // 将子路由的path设为空字符串
        name: 'Home',
        component: Home,
        meta: { requiresAuth: false }
      }
    ]
  },
  {
    path: '/moyu',
    name: 'moyu',
    component: () => import('@/components/iframe/ImageCard.vue'),
  },
  {
    path: '/multi-iframe',
    name: 'MultiIframe',
    component: () => import('@/views/MultiIframePage.vue'),
    meta: { requiresAuth: false }
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.VITE_APP_BUILD_PATH),
  routes,
  linkActiveClass: 'active'
})

// 添加全局路由守卫来记录上次访问的路由
router.beforeEach((to, from, next) => {
  console.log(to.path,'topath')
  // 处理路由记忆功能
  if (to.path === '/office') {
    // 获取 wallpaper store 实例
    const wallpaperStore = useWallpaperStore()
    // 记录这两个路径之间的跳转
    localStorage.setItem('currentDataSource', 'office');
    // 清除纯净模式相关标记，确保进入办公模式
    localStorage.setItem('isPureMode', 'false');
    localStorage.removeItem('shouldEnterPureMode');
    // 使用路由专用方法设置壁纸为索引 1（第二张壁纸）
    wallpaperStore.setWallpaperByRoute(1)
    console.log('已设置办公模式壁纸为索引 1，并清除纯净模式标记')

    router.push('/')
  }else if(to.path === '/fun'){
    // 获取 wallpaper store 实例
    const wallpaperStore = useWallpaperStore()
    // 记录这两个路径之间的跳转
    localStorage.setItem('currentDataSource', 'entertainment');
    // 清除纯净模式相关标记，确保进入娱乐模式
    localStorage.setItem('isPureMode', 'false');
    localStorage.removeItem('shouldEnterPureMode');
    // 使用路由专用方法设置壁纸为索引 0（第一张壁纸）
    wallpaperStore.setWallpaperByRoute(0)
    console.log('已设置娱乐模式壁纸为索引 0，并清除纯净模式标记')
    router.push('/')
  }else if(to.path === '/lite'){
    // 获取 wallpaper store 实例
    const wallpaperStore = useWallpaperStore()
    // 记录这两个路径之间的跳转
    localStorage.setItem('currentDataSource', 'office');
    // 使用路由专用方法设置壁纸为索引 1（第二张壁纸）
    wallpaperStore.setWallpaperByRoute(1)
    // 设置标记，指示需要进入纯净模式（避免时序问题）
    localStorage.setItem('shouldEnterPureMode', 'true')
    router.push('/')
  }
  // 继续正常导航
  next();
});

export default router 