import { defineStore } from 'pinia'
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import cat from '@/assets/officialWallpaper/mian.png'
import officeBg from '@/assets/officialWallpaper/img29.jpg'
import { useRoute, useRouter } from 'vue-router'
import { getOfficePaper, getAllPublickWallpaper, listDownloadUserWallpaper, listUserCollectWallpaper, listUserWallpaper } from '@/api/paper.js'

// 雪山
import xs from '@/assets/officialWallpaper/publicWallpaper/xueshan.webp'
// 下雨
import xy from '@/assets/officialWallpaper/publicWallpaper/xiayu.webp'


export const useWallpaperStore = defineStore('wallpaper', () => {
  // 状态定义
  const wallpaperUrl = ref(localStorage.getItem('wallpaperApiUrl') || 'https://bing.img.run/rand_uhd.php')
  const blurAmount = ref(parseInt(localStorage.getItem('wallpaperBlur') || '0'))
  const useRandomBg = ref(localStorage.getItem('useRandomBg') === 'true')
  const bgTransparent = ref(localStorage.getItem('bgTransparent') === 'true')
  const isRefreshing = ref(false)
  const currentBgUrl = ref(localStorage.getItem('currentBgImage') || '')
  const router = useRouter()
  const route = useRoute();
  // 新增 - 壁纸模式 (1:自定义上传, 2:推荐壁纸, 3:随机API)
  const wallpaperMode = ref(parseInt(localStorage.getItem('wallpaperMode') || '2'))

  // 自动刷新壁纸定时器
  let autoRefreshTimer = null
  
  // 新增 - 自定义上传的壁纸URL
  const userCustomWallpaper = ref(localStorage.getItem('userCustomWallpaper') || '')
  
  // 新增 - 推荐壁纸列表和当前选中的推荐壁纸索引
  const officialWallpapers = ref([
    // {
    //   id: 1,
    //   name: '快乐咪咪',
    //   sortUrl: cat,
    //   url: cat,
    //   dataSource: 'entertainment' // 娱乐模式默认壁纸
    // },
    // {
    //   id: 2,
    //   name: '办公背景',
    //   sortUrl: officeBg,
    //   url: officeBg,
    //   dataSource: 'office' // 办公模式默认壁纸
    // }
  ])

  const userPublicWallpapers = ref([])
  const userDownloadedWallpapers = ref([])
  const userFavoriteWallpapers = ref([])
  const isLoadingUserFavoriteWallpapers = ref(false)
 
 const userWallpapers = ref([]);
 const isLoadingUserWallpapers = ref(false);

   const selectedOfficialWallpaper = ref(parseInt(localStorage.getItem('selectedOfficialWallpaper') || '0'))
  // 新增 - 跟踪用户是否手动设置过壁纸
  const isUserCustomized = ref(localStorage.getItem('isUserCustomizedWallpaper') === 'true')

  // API数据加载状态管理
  const isLoadingWallpapers = ref(false)
  const wallpapersLoadError = ref(null)
  const wallpapersLastFetch = ref(null)
  
  // 新增 - 分页状态管理
  const currentPage = ref(1)
  const pageSize = ref(100)
  const hasMoreWallpapers = ref(true)
  const isLoadingMore = ref(false)

  // 获取官方推荐壁纸数据
  async function fetchOfficialWallpapers(forceRefresh = false) {
    // 检查缓存，避免重复请求（缓存1小时）
    const now = Date.now()
    const cacheExpiry = 5000 // 5秒

    if (!forceRefresh && wallpapersLastFetch.value && (now - wallpapersLastFetch.value) < cacheExpiry) {
      return
    }

    if (isLoadingWallpapers.value) {
      return
    }

    try {
      isLoadingWallpapers.value = true
      wallpapersLoadError.value = null
      
      // 重置分页状态
      currentPage.value = 1
      hasMoreWallpapers.value = true

      const response = await getOfficePaper(currentPage.value, pageSize.value)

      if (response.status === 200 && response.data && Array.isArray(response.data)) {
        // 转换API数据格式为内部格式
        const apiWallpapers = response.data.map(item => ({
          id: item.id,
          name: item.imgName || `壁纸_${item.id}`,
          url: item.imgUrl,
          sortUrl: item.compressUrl,
          sort: item.sort || 999,
          type: item.type,
          classificationId: item.classificationId,
          classificationName: item.classificationName,
          createtime: item.createtime,
          md5: item.md5,
          code: item.code
        }))

        // 按sort字段排序
        apiWallpapers.sort((a, b) => a.sort - b.sort)

        // 保留本地默认壁纸（前两个），然后添加API壁纸
        // const localDefaults = officialWallpapers.value.slice(0, 2) // 快乐咪咪和办公背景
        officialWallpapers.value = [...apiWallpapers]

        // 检查是否有更多数据可加载
        hasMoreWallpapers.value = response.data.length >= pageSize.value

        wallpapersLastFetch.value = now

      } else {
        throw new Error('API返回数据格式错误')
      }

    } catch (error) {
      console.error('获取推荐壁纸失败:', error)
      wallpapersLoadError.value = error.message || '获取推荐壁纸失败'

      // API失败时使用本地默认壁纸，不显示错误消息给用户

    } finally {
      isLoadingWallpapers.value = false
    }
  }

  // 获取用户共享壁纸
  async function fetchUserPublicWallpapers() {
    if (isLoadingWallpapers.value) {
      return
    }
    try {
      isLoadingWallpapers.value = true
      const response = await getAllPublickWallpaper()
      if (response.status === 200 && response.data && Array.isArray(response.data)) {
        userPublicWallpapers.value = response.data.map(item => ({
          id: item.id,
          name: item.imgName || `壁纸_${item.id}`,
          url: item.imgUrl,
          sortUrl: item.compressUrl,
          sort: item.sort || 999,
          type: item.type,
          classificationId: item.classificationId,
          classificationName: item.classificationName,
          createtime: item.createtime,
          md5: item.md5,
          code: item.code
        }))
      } else {
        throw new Error('API返回数据格式错误')
      }
    } catch (error) {
      console.error('获取用户共享壁纸失败:', error)
      message.error('获取用户共享壁纸失败')
    } finally {
      isLoadingWallpapers.value = false
    }
  }

  // 获取用户下载壁纸
  async function fetchUserDownloadedWallpapers() {
    if (isLoadingWallpapers.value) {
      return
    }
    try {
      isLoadingWallpapers.value = true
      const response = await listDownloadUserWallpaper()
      if (response.status === 200 && response.data && Array.isArray(response.data)) {
        userDownloadedWallpapers.value = response.data.map(item => ({
          id: item.id,
          name: item.imgName || `壁纸_${item.id}`,
          url: item.imgUrl,
          sortUrl: item.compressUrl,
          sort: item.sort || 999,
          type: item.type,
          classificationId: item.classificationId,
          classificationName: item.classificationName,
          createtime: item.createtime,
          md5: item.md5,
          code: item.code
        }))
      } else {
        throw new Error('API返回数据格式错误')
      }
    } catch (error) {
      console.error('获取用户下载壁纸失败:', error)
      message.error('获取用户下载壁纸失败')
    } finally {
      isLoadingWallpapers.value = false
    }
  }
  
  // 获取用户收藏壁纸
  async function fetchUserFavoriteWallpapers() {
    if (isLoadingUserFavoriteWallpapers.value) return;
    isLoadingUserFavoriteWallpapers.value = true;
    try {
      const response = await listUserCollectWallpaper();
      if (response.status === 200 && Array.isArray(response.data)) {
        userFavoriteWallpapers.value = response.data.map(item => ({
          ...item,
          id: item.id,
          name: item.imgName || `壁纸_${item.id}`,
          url: item.imgUrl,
          sortUrl: item.compressUrl,
          code: item.code,
          isFavorite: true,
        }));
      } else {
        userFavoriteWallpapers.value = [];
      }
    } catch (error) {
      console.error('获取用户收藏壁纸失败:', error);
      message.error('获取用户收藏壁纸失败');
      userFavoriteWallpapers.value = [];
    } finally {
      isLoadingUserFavoriteWallpapers.value = false;
    }
  }
  
  // 获取用户壁纸
  async function fetchUserWallpapers() {
    if (isLoadingUserWallpapers.value) return;
    isLoadingUserWallpapers.value = true;
    try {
      const response = await listUserWallpaper();
      if (response.status === 200 && Array.isArray(response.data)) {
        userWallpapers.value = response.data.map(item => ({
            id: item.id,
            name: item.imgName || `壁纸_${item.id}`,
            url: item.imgUrl,
            sortUrl: item.compressUrl,
            sort: item.sort || 999,
            type: item.type,
            classificationId: item.classificationId,
            classificationName: item.classificationName,
            createtime: item.createtime,
            md5: item.md5,
            code: item.code
          }))
      } else {
        userWallpapers.value = [];
      }
    } catch (error) {
      console.error('获取用户壁纸失败:', error);
      message.error('获取用户壁纸失败');
      userWallpapers.value = [];
    } finally {
      isLoadingUserWallpapers.value = false;
    }
  }
  
  // 新增 - 加载更多壁纸
  async function loadMoreWallpapers() {
    // 如果没有更多数据或正在加载中，则不执行
    if (!hasMoreWallpapers.value || isLoadingMore.value || isLoadingWallpapers.value) {
      return
    }
    
    try {
      isLoadingMore.value = true
      
      // 增加页码
      currentPage.value++
      
      const response = await getOfficePaper(currentPage.value, pageSize.value)
      
      if (response.status === 200 && response.data && Array.isArray(response.data)) {
        // 转换API数据格式为内部格式
        const apiWallpapers = response.data.map(item => ({
          id: item.id,
          name: item.imgName || `壁纸_${item.id}`,
          url: item.imgUrl,
          sortUrl: item.compressUrl,
          sort: item.sort || 999,
          type: item.type,
          classificationId: item.classificationId,
          classificationName: item.classificationName,
          createtime: item.createtime,
          md5: item.md5,
          code: item.code
        }))
        
        // 按sort字段排序
        apiWallpapers.sort((a, b) => a.sort - b.sort)
        
        // 将新数据追加到现有数据后面
        officialWallpapers.value = [...officialWallpapers.value, ...apiWallpapers]
        
        // 检查是否有更多数据可加载
        hasMoreWallpapers.value = response.data.length >= pageSize.value
        
      } else {
        throw new Error('加载更多壁纸时API返回数据格式错误')
      }
    } catch (error) {
      console.error('加载更多壁纸失败:', error)
      message.error('加载更多壁纸失败，请重试')
      
      // 失败时回滚页码
      currentPage.value--
    } finally {
      isLoadingMore.value = false
    }
  }

  // 根据数据源设置默认壁纸（仅在用户未自定义时）
  function setDefaultWallpaperByDataSource(dataSource) {
    // 如果用户已经自定义过壁纸，不自动切换
    if (isUserCustomized.value) {
      return
    }
    // 查找对应数据源的默认壁纸
    const defaultWallpaperIndex = officialWallpapers.value.findIndex(
      wallpaper => wallpaper.dataSource === dataSource
    )

    if (defaultWallpaperIndex !== -1) {
      // 设置为官方壁纸模式
      wallpaperMode.value = 2
      localStorage.setItem('wallpaperMode', '2')

      // 选择对应的官方壁纸
      selectedOfficialWallpaper.value = defaultWallpaperIndex
      localStorage.setItem('selectedOfficialWallpaper', defaultWallpaperIndex.toString())

      // 应用壁纸
      const selectedWallpaper = officialWallpapers.value[defaultWallpaperIndex]
      document.documentElement.style.backgroundImage = `url(${selectedWallpaper.url})`
      currentBgUrl.value = selectedWallpaper.url
      localStorage.setItem('currentBgImage', selectedWallpaper.url)

    } else {
      console.warn(`未找到数据源${dataSource}对应的默认壁纸`)
    }
  }

  // 设置壁纸模式
  function setWallpaperMode(mode) {
    wallpaperMode.value = mode
    localStorage.setItem('wallpaperMode', mode.toString())

    // 标记为用户自定义
    isUserCustomized.value = true
    localStorage.setItem('isUserCustomizedWallpaper', 'true')

    // 根据不同模式应用相应壁纸
    applyWallpaperByMode()

    // 注意：不在这里强制启动或停止自动刷新，尊重用户的明确选择
    // 只有在模式1（自定义上传）时才停止自动刷新，因为自定义上传不支持自动刷新
    if (mode === 1) {
      stopAutoRefresh()
    }
    // 对于模式2和3，保持用户当前的自动刷新设置不变
  }

  // 根据当前模式应用壁纸
  function applyWallpaperByMode() {
    switch(wallpaperMode.value) {
      case 1: // 自定义上传
        if (userCustomWallpaper.value) {
          document.documentElement.style.backgroundImage = `url(${userCustomWallpaper.value})`
          currentBgUrl.value = userCustomWallpaper.value
          localStorage.setItem('currentBgImage', userCustomWallpaper.value)
        }
        break
      case 2: // 官方壁纸
        if (officialWallpapers.value.length > 0 && selectedOfficialWallpaper.value < officialWallpapers.value.length) {
          const selectedWallpaper = officialWallpapers.value[selectedOfficialWallpaper.value].url
          document.documentElement.style.backgroundImage = `url(${selectedWallpaper})`
          currentBgUrl.value = selectedWallpaper
          localStorage.setItem('currentBgImage', selectedWallpaper)
          localStorage.setItem('selectedOfficialWallpaper',selectedOfficialWallpaper.value)
        }
        break
      case 3: // 随机API
        refreshWallpaper()
        break
      default:
        refreshWallpaper()
    }
  }

  // 设置自定义上传壁纸
  function setCustomWallpaper(url) {
    userCustomWallpaper.value = url
    localStorage.setItem('userCustomWallpaper', url)

    // 标记为用户自定义
    isUserCustomized.value = true
    localStorage.setItem('isUserCustomizedWallpaper', 'true')

    // 如果当前是自定义模式，立即应用壁纸
    if (wallpaperMode.value === 1) {
      document.documentElement.style.backgroundImage = `url(${url})`
      currentBgUrl.value = url
      localStorage.setItem('currentBgImage', url)
    }
  }

  // 选择官方壁纸
  function selectOfficialWallpaper(index) {
    if (index >= 0 && index < officialWallpapers.value.length) {
      selectedOfficialWallpaper.value = index
      localStorage.setItem('selectedOfficialWallpaper', index.toString())

      // 标记为用户自定义
      isUserCustomized.value = true
      localStorage.setItem('isUserCustomizedWallpaper', 'true')

      // 如果当前是官方壁纸模式，立即应用壁纸
      if (wallpaperMode.value === 2) {
        const selectedWallpaper = officialWallpapers.value[index].url
        document.documentElement.style.backgroundImage = `url(${selectedWallpaper})`
        currentBgUrl.value = selectedWallpaper
        localStorage.setItem('currentBgImage', selectedWallpaper)
      }
    }
  }

  // 路由切换时设置壁纸（不标记为用户自定义）
  async function setWallpaperByRoute(index) {
    // 如果用户已经自定义过壁纸，不自动切换
    if (isUserCustomized.value) {
      return
    }

    // 设置路由壁纸标记，防止初始化覆盖
    localStorage.setItem('routeWallpaperSetting', 'true')
    localStorage.setItem('routeWallpaperIndex', index.toString())

    // 如果壁纸数据未加载，先加载数据
    if (officialWallpapers.value.length === 0) {
      console.log('路由切换：壁纸数据未加载，正在加载...')
      await fetchOfficialWallpapers()
    }

    // 确保索引有效且壁纸数据已加载
    if (index >= 0 && index < officialWallpapers.value.length) {
      // 设置为推荐壁纸模式
      wallpaperMode.value = 2
      localStorage.setItem('wallpaperMode', '2')

      // 选择指定索引的壁纸
      selectedOfficialWallpaper.value = index
      localStorage.setItem('selectedOfficialWallpaper', index.toString())

      // 立即应用壁纸
      const selectedWallpaper = officialWallpapers.value[index].url
      document.documentElement.style.backgroundImage = `url(${selectedWallpaper})`
      currentBgUrl.value = selectedWallpaper
      localStorage.setItem('currentBgImage', selectedWallpaper)

      console.log(`路由切换：自动设置壁纸为索引 ${index}`)

      // 延迟清除路由设置标记，给初始化足够时间完成
      setTimeout(() => {
        localStorage.removeItem('routeWallpaperSetting')
        localStorage.removeItem('routeWallpaperIndex')
      }, 2000)
    } else {
      console.warn(`路由切换：无效的壁纸索引 ${index} 或壁纸数据加载失败`)
      // 清除无效的路由设置标记
      localStorage.removeItem('routeWallpaperSetting')
      localStorage.removeItem('routeWallpaperIndex')
    }
  }

  // 设置壁纸API地址
  function setWallpaperUrl(url) {
    wallpaperUrl.value = url
    localStorage.setItem('wallpaperApiUrl', url)
    
    // 如果当前是随机API模式，立即刷新壁纸
    // if (wallpaperMode.value === 3) {
      refreshWallpaper()
    // }
  }

  // 设置模糊度
  function setBlurAmount(amount) {
    blurAmount.value = amount
    localStorage.setItem('wallpaperBlur', amount.toString())
    applyBlurEffect()
  }

  // 切换随机背景开关
  function toggleRandomBg() {
    useRandomBg.value = !useRandomBg.value
    localStorage.setItem('useRandomBg', useRandomBg.value.toString())
    
    if (useRandomBg.value && wallpaperMode.value === 3) {
      refreshWallpaper()
    } else if (!useRandomBg.value && wallpaperMode.value === 3) {
      // 关闭随机背景，移除背景图
      document.documentElement.style.backgroundImage = ''
      localStorage.removeItem('currentBgImage')
      currentBgUrl.value = ''
    }
  }

  // 切换背景透明开关
  function toggleBgTransparent() {
    bgTransparent.value = !bgTransparent.value
    localStorage.setItem('bgTransparent', bgTransparent.value.toString())
    
    // 应用透明效果
    applyTransparentEffect()
  }

  // 应用透明效果
  function applyTransparentEffect() {
    if (bgTransparent.value) {
      document.documentElement.classList.add('transparent-bg')
    } else {
      document.documentElement.classList.remove('transparent-bg')
    }
  }

  // 应用模糊效果
  function applyBlurEffect() {
    document.documentElement.style.setProperty('--wallpaper-blur', `${blurAmount.value}px`)
  }

  // 刷新壁纸
  function refreshWallpaper() {
    // 仅在随机API模式下使用此功能
    // if (wallpaperMode.value !== 3) {
    //   // 对于其他模式，根据当前模式显示不同的成功提示
    //   if (wallpaperMode.value === 1) {
    //     message.success('已切换为自定义壁纸')
    //   } else if (wallpaperMode.value === 2) {
    //     message.success('已切换为官方壁纸，官方壁纸由官方提供，不支持刷新')
    //   }
    //   return
    // }

    // 防抖处理
    if (isRefreshing.value) {
      // message.warning('壁纸刷新中，请稍后再试')
      return
    }

    isRefreshing.value = true


    // 简化逻辑，直接获取新URL
    const newBgUrl = `${wallpaperUrl.value}?t=${Date.now()}`

    // 预加载图片
    const img = new Image()

    // 设置超时机制，确保即使图片加载失败也能重新刷新
    const timeoutId = setTimeout(() => {
      
      message.error('壁纸加载超时，请重试')
      isRefreshing.value = false
    }, 5000) // 5秒超时

    img.onload = () => {
      clearTimeout(timeoutId) // 清除超时计时器

      // 成功加载后直接更新状态
      currentBgUrl.value = newBgUrl
      document.documentElement.style.backgroundImage = `url(${newBgUrl})`
      localStorage.setItem('currentBgImage', newBgUrl)

      // 显示成功消息
      // message.success('壁纸刷新成功')

      // 延迟重置刷新状态
      setTimeout(() => {
        isRefreshing.value = false

      }, 500)
    }

    img.onerror = () => {
      clearTimeout(timeoutId) // 清除超时计时器
      console.error('壁纸加载失败')
      message.error('壁纸加载失败，请检查网络连接')
      isRefreshing.value = false
    }

    // 开始加载图像
    img.src = newBgUrl

    if (!useRandomBg.value) {
      useRandomBg.value = true
      localStorage.setItem('useRandomBg', 'true')
    }
  }

  // HeaderBar专用：从推荐壁纸库随机刷新壁纸
  function refreshWallpaperFromRecommended() {
    // 防抖处理
    if (isRefreshing.value) {
      return
    }

    isRefreshing.value = true

    // 从推荐壁纸库中随机选择壁纸（排除当前选中的）
    const availableWallpapers = officialWallpapers.value.filter((_, index) => index !== selectedOfficialWallpaper.value)
    const randomIndex = Math.floor(Math.random() * availableWallpapers.length)
    const selectedWallpaper = availableWallpapers[randomIndex]
    const newBgUrl = selectedWallpaper.url

    // 预加载图片
    const img = new Image()

    // 设置超时机制
    const timeoutId = setTimeout(() => {
      message.error('壁纸加载超时，请重试')
      isRefreshing.value = false
    }, 5000)

    img.onload = () => {
      clearTimeout(timeoutId)

      // 成功加载后更新状态
      currentBgUrl.value = newBgUrl
      document.documentElement.style.backgroundImage = `url(${newBgUrl})`
      localStorage.setItem('currentBgImage', newBgUrl)

      // 更新选中的推荐壁纸索引
      const originalIndex = officialWallpapers.value.findIndex(w => w.url === selectedWallpaper.url)
      selectedOfficialWallpaper.value = originalIndex
      localStorage.setItem('selectedOfficialWallpaper', originalIndex.toString())

      // 延迟重置刷新状态
      setTimeout(() => {
        isRefreshing.value = false
      }, 500)
    }

    img.onerror = () => {
      clearTimeout(timeoutId)
      console.error('壁纸加载失败')
      message.error('壁纸加载失败，请重试')
      isRefreshing.value = false
    }

    // 开始加载图像
    img.src = newBgUrl

    // 确保随机背景开关开启
    if (!useRandomBg.value) {
      useRandomBg.value = true
      localStorage.setItem('useRandomBg', 'true')
    }
  }

  // 添加自动刷新状态，从localStorage读取保存的状态
  const autoRefreshActive = ref(localStorage.getItem('autoRefreshActive') !== 'false')

  // 启动自动刷新定时器
  function startAutoRefresh() {
    // 清除现有定时器
    stopAutoRefresh()

    // 设置自动刷新状态为激活并保存到localStorage
    autoRefreshActive.value = true
    localStorage.setItem('autoRefreshActive', 'true')

    // 只在模式2或3时启动定时器
    if (wallpaperMode.value === 2 || wallpaperMode.value === 3) {
      autoRefreshTimer = setInterval(() => {
        if (wallpaperMode.value === 2) {
          // 推荐壁纸模式：从推荐壁纸库随机选择
          refreshWallpaperFromRecommended()
        } else if (wallpaperMode.value === 3) {
          // 随机API模式：调用随机API
          refreshWallpaper()
        }
      }, 2 * 1000 * 60) // 2分钟
    }
  }

  // 停止自动刷新定时器
  function stopAutoRefresh() {
    if (autoRefreshTimer) {
      clearInterval(autoRefreshTimer)
      autoRefreshTimer = null
    }
    // 设置自动刷新状态为非激活并保存到localStorage
    autoRefreshActive.value = false
    localStorage.setItem('autoRefreshActive', 'false')
  }

  // 强制刷新随机API壁纸（专门用于Office页面）
  function forceRefreshRandomWallpaper() {
    // 防抖处理
    if (isRefreshing.value) {
      // message.warning('壁纸刷新中，请稍后再试')
      return
    }

    isRefreshing.value = true
    

    // 强制使用随机API获取新壁纸
    const newBgUrl = `${wallpaperUrl.value}?t=${Date.now()}`

    // 预加载图片
    const img = new Image()

    // 设置超时机制
    const timeoutId = setTimeout(() => {
      
      message.error('壁纸加载超时，请重试')
      isRefreshing.value = false
    }, 5000) // 5秒超时

    img.onload = () => {
      clearTimeout(timeoutId) // 清除超时计时器

      // 成功加载后直接更新状态
      currentBgUrl.value = newBgUrl
      document.documentElement.style.backgroundImage = `url(${newBgUrl})`
      localStorage.setItem('currentBgImage', newBgUrl)

      // 确保随机背景开关开启
      if (!useRandomBg.value) {
        useRandomBg.value = true
        localStorage.setItem('useRandomBg', 'true')
      }

      // 延迟重置刷新状态
      setTimeout(() => {
        isRefreshing.value = false
        
      }, 500)
    }

    img.onerror = () => {
      clearTimeout(timeoutId) // 清除超时计时器
      console.error('壁纸加载失败')
      message.error('壁纸加载失败，请检查网络连接')
      isRefreshing.value = false
    }

    // 开始加载图像
    img.src = newBgUrl
  }

  // 初始化壁纸设置
  async function initWallpaper() {
    // 确保默认设置清晰可见，无模糊与半透明
    // 重置localStorage中可能存在的值
    if (!localStorage.getItem('wallpaperBlur')) {
      blurAmount.value = 0
      localStorage.setItem('wallpaperBlur', '0')
    }

    if (!localStorage.getItem('bgTransparent')) {
      bgTransparent.value = true
      localStorage.setItem('bgTransparent', 'true')
    }

    // 应用模糊与透明效果
    applyBlurEffect()
    applyTransparentEffect()

    // 获取官方推荐壁纸数据
    await fetchOfficialWallpapers()

    // 检查是否有路由设置的壁纸，如果有则不应用默认壁纸模式
    const hasRouteWallpaperSetting = localStorage.getItem('routeWallpaperSetting') === 'true'
    const routeWallpaperIndex = localStorage.getItem('routeWallpaperIndex')

    if (hasRouteWallpaperSetting && routeWallpaperIndex !== null) {
      console.log('初始化：检测到路由壁纸设置，跳过默认壁纸应用')
      // 如果路由设置的索引有效，确保应用正确的壁纸
      const index = parseInt(routeWallpaperIndex)
      if (index >= 0 && index < officialWallpapers.value.length) {
        selectedOfficialWallpaper.value = index
        const selectedWallpaper = officialWallpapers.value[index].url
        document.documentElement.style.backgroundImage = `url(${selectedWallpaper})`
        currentBgUrl.value = selectedWallpaper
        localStorage.setItem('currentBgImage', selectedWallpaper)
        console.log(`初始化：应用路由设置的壁纸索引 ${index}`)
      }
    } else {
      // 没有路由设置时，根据当前模式应用壁纸
      applyWallpaperByMode()
    }

    // 根据保存的自动刷新状态和当前模式决定是否启动自动刷新
    if (autoRefreshActive.value && (wallpaperMode.value === 2 || wallpaperMode.value === 3)) {
      startAutoRefresh()
    }
  }

  // 打开壁纸面板
  function openWallpaperPanel() {
    // 发送自定义事件，通知其他组件打开壁纸设置面板
    window.dispatchEvent(new CustomEvent('open-wallpaper-panel'))
  }

  return {
    wallpaperUrl,
    blurAmount,
    useRandomBg,
    bgTransparent,
    isRefreshing,
    currentBgUrl,
    // 新增导出
    wallpaperMode,
    userCustomWallpaper,
    officialWallpapers,
    userPublicWallpapers,
    userDownloadedWallpapers,
    userFavoriteWallpapers,
    userWallpapers,
    selectedOfficialWallpaper,
    isUserCustomized,
    // API状态导出
    isLoadingWallpapers,
    isLoadingUserFavoriteWallpapers,
    isLoadingUserWallpapers,
    wallpapersLoadError,
    wallpapersLastFetch,
    // 新增分页状态导出
    currentPage,
    pageSize,
    hasMoreWallpapers,
    isLoadingMore,
    // 原有方法
    setWallpaperUrl,
    setBlurAmount,
    toggleRandomBg,
    toggleBgTransparent,
    refreshWallpaper,
    refreshWallpaperFromRecommended,
    forceRefreshRandomWallpaper,
    initWallpaper,
    // 新增方法
    setWallpaperMode,
    setCustomWallpaper,
    selectOfficialWallpaper,
    setWallpaperByRoute,
    applyWallpaperByMode,
    setDefaultWallpaperByDataSource,
    openWallpaperPanel,
    fetchOfficialWallpapers,
    fetchUserPublicWallpapers,
    fetchUserDownloadedWallpapers,
    fetchUserFavoriteWallpapers,
    fetchUserWallpapers,
    loadMoreWallpapers, // 新增加载更多方法
    // 自动刷新控制
    startAutoRefresh,
    stopAutoRefresh,
    // 新增自动刷新状态导出
    autoRefreshActive
  }
})