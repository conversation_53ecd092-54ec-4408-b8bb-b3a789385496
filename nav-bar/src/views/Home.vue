<template>
  <div class="home-container" @dblclick="handleDoubleClick" @contextmenu.prevent="showContextMenu($event)"
    :style="{ paddingBottom: containerPaddingBottom }">
    <button @click="getLayoutInfo" class="getLayoutInfo">
      <img src="https://beian.mps.gov.cn/web/assets/logo01.6189a29f.png" style="width: 20px;" />
      <a href="https://beian.mps.gov.cn/#/query/webSearch?code=50019002504854" rel="noreferrer" target="_blank" style="color: white;">渝公网安备50019002504854号</a>
      &nbsp;<a>渝ICP备2025057928号-1</a>
    </button>
    <!-- 顶部时间区域 -->
    <TimeDisplay
      :class="{ 'pure-mode-animation': isPureMode }"
      :style="timeDisplayStyle"
      :external-font-size="timeFontConfig.fontSize"
      :external-font-weight="timeFontConfig.fontWeight"
    />

    <!-- 搜索区域 -->
    <SearchEngine placeholder="搜索..." @query="updateSearchQuery" :class="{ 'pure-mode-animation': isPureMode }" :style="searchEngineStyle" />

    <!-- 应用网格 -->
    <div class="apps-section" v-show="!isPureMode">
      <div ref="gridContainer" class="grid-container">
        <!--   name="flip-list"  -->
        <transition-group tag="div" class="app-grid" :key="activeCategory">
          <div v-for="(app, index) in displayedApps" :key="app.id || `dynamic-app-${index}-${Date.now()}`"
            class="app-item" :class="[
              `size-${app.size?.w || 1}x${app.size?.h || 1}`,
              // `card-animate-${currentCardAnimation}`,
              { 'hidden': !isAppVisible(app) },
              { 'add-icon-item': app.type === 'add-icon' }
            ]" :style="[
              { '--card-color': app.color },
              isEditMode ? {
                // animation: 'shake-edit ease .3s infinite !important',
                transformOrigin: 'center !important',
                perspective: '1000px !important',
                willChange: 'transform !important',
                backfaceVisibility: 'hidden !important',
              } : {}
            ]" :data-id="app.id" :data-size-w="app.size?.w || 1" :data-size-h="app.size?.h || 1"
            :data-is-folder="app.type === 'folder'"
            :data-is-collection="app.type === 'collection' || app.type === 'collect'"
            :data-is-add-icon="app.type === 'add-icon'"
            :draggable="!shouldDisableDrag && app.type !== 'add-icon'"
            @dragstart="!shouldDisableDrag && app.type !== 'add-icon' && handleDragStart($event, app)"
            @contextmenu.prevent.stop="app.type !== 'add-icon' && showContextMenu($event, app)">
            <!-- 删除按钮 - 仅在编辑模式下显示，但不显示在"添加图标"应用上 -->
            <div v-if="isEditMode && app.type !== 'add-icon'" class="delete-icon" @click.stop="confirmDeleteApp(app)">
              ×
            </div>

            <!-- 普通应用卡片 -->
            <Tooltip v-if="app.type === 'app'" :title="app.description" placement="right" :mouseEnterDelay="0.5">
              <div class="app-card" :style="isEditMode ? {
                animation: 'shake-edit ease .3s infinite !important',
                transformOrigin: 'center !important',
                perspective: '1000px !important',
                willChange: 'transform !important',
                backfaceVisibility: 'hidden !important',
              } : {}" @click="openApp(app)">
                <div class="app-icon" :style="{ 'background-color': app.color || '#ffffff' }">
                  <img v-if="app.icon" style="width: 100%;height: 100%; border-radius: 15px;object-fit: contain"
                    :src="concatUrl(app.icon)" alt="icon" />
                  <div v-else class="app-icon-div">
                    <span v-text-scale="app.name.slice(0, 6)" class="app-icon-text">{{ app.name.slice(0, 6) }}</span>
                  </div>
                  <div v-if="app.iscanopen == 2" class="newWindow">
                    <svg t="1748943554105" class="icon" viewBox="0 0 1024 1024" version="1.1"
                      xmlns="http://www.w3.org/2000/svg" p-id="5226" width="20" height="20">
                      <path
                        d="M914.285714 914.285714h-804.571428v-804.571428h248.685714V0H109.714286C51.2 0 0 51.2 0 109.714286v797.257143c0 65.828571 51.2 117.028571 109.714286 117.028571h797.257143c65.828571 0 109.714286-51.2 109.714285-109.714286V658.285714h-109.714285v256h7.314285zM629.028571 0v109.714286h204.8L277.942857 665.6l80.457143 80.457143 555.885714-555.885714v204.8H1024V0H629.028571z"
                        fill="#999999" p-id="5227"></path>
                    </svg>
                  </div>
                </div>
                <div class="app-name">{{ app.name }}</div>
                <!-- <div v-if="app.description" class="app-description">{{ app.description }}</div> -->
              </div>
            </Tooltip>

            <!-- 文件夹卡片 -->
            <Tooltip v-else-if="app.type === 'folder'"
              :title="app.description || `拖动应用到此文件夹 (${app.children?.length || 0}个应用)`" placement="right"
              :mouseEnterDelay="0.5">
              <div class="folder-card" :style="isEditMode ? {
                animation: 'shake-edit ease .3s infinite !important',
                transformOrigin: 'center !important',
                perspective: '1000px !important',
                willChange: 'transform !important',
                backfaceVisibility: 'hidden !important',
              } : {}" @click="openFolderModal(app)">
                <div class="folder-top">
                  <div v-if="app.children && app.children.length > 0" class="folder-grid"
                    :style="getFolderGridStyle(app)">
                    <div v-for="childApp in getFolderDisplayItems(app)" :key="`folder-preview-${childApp.id}`"
                      class="folder-grid-item">
                      <img v-if="childApp.icon" :src="concatUrl(childApp.icon)" alt="app-icon" />
                      <div v-else class="app-icon-div">
                        <span v-text-scale="childApp.name.slice(0, 6)" class="app-icon-text">{{ childApp.name.slice(0,
                          6) }}</span>
                      </div>
                    </div>
                  </div>
                  <span v-else class="folder-icon">📁</span>
                </div>
                <div class="app-name">{{ app.name }} </div>
              </div>
            </Tooltip>

            <!-- 应用合集卡片 -->
            <Tooltip v-else-if="app.type === 'collection' || app.type === 'collect'"
              :title="app.description || `应用合集 (${app.children?.length || 0}个应用)`" placement="right"
              :mouseEnterDelay="0.5">
              <!-- {{ app }} -->
              <CollectionCard class="collect-card" :collection="app" @open-collection="openCollectionModal" :style="isEditMode ? {
                animation: 'shake-edit ease .3s infinite !important',
                transformOrigin: 'center !important',
                perspective: '1000px !important',
                willChange: 'transform !important',
                backfaceVisibility: 'hidden !important',
              } : {}" />
            </Tooltip>

            <!-- 小组件类型 -->
            <Tooltip v-else-if="app.type === 'card'" :title="app.name" placement="right" :mouseEnterDelay="0.5">
              <!-- 直接把app.websiteAddress当成组件名，通过getComponentByName获取对应组件 -->
              <component v-if="app.websiteAddress" :is="getComponentByName(app.websiteAddress)" class="card-component"
                :url="app.icon" :appId="String(app.id)" :title="app.name" :size="app.size || { w: 2, h: 2 }"
                :headerColor="app.headerColor" :style="isEditMode ? {
                  animation: 'shake-edit ease .3s infinite !important',
                  transformOrigin: 'center !important',
                  perspective: '1000px !important',
                  willChange: 'transform !important',
                  backfaceVisibility: 'hidden !important',
                } : {}"></component>
              <div v-else class="card-placeholder">
                未指定组件名称
              </div>
              <div class="app-name">{{ app.name }}</div>

            </Tooltip>

            <!-- 添加图标应用 -->
            <Tooltip v-else-if="app.type === 'add-icon'" :title="app.description" placement="right" :mouseEnterDelay="0.5">
              <div class="add-icon-card" :style="isEditMode ? {
                animation: 'shake-edit ease .3s infinite !important',
                transformOrigin: 'center !important',
                perspective: '1000px !important',
                willChange: 'transform !important',
                backfaceVisibility: 'hidden !important',
              } : {}" @click="showAddIconModal" @contextmenu.prevent.stop>
                <div class="add-icon-container">
                  <img :src="customAdd" />
                </div>
                <div class="app-name">{{ app.name }}</div>
              </div>
            </Tooltip>

            <!-- 其他自定义卡片 - 通过动态组件实现 -->
            <div class="custom-component-wrapper" v-else-if="app.type && app.type !== 'app'">
              <component :is="getCardComponent(app.type)" class="card-component" :url="app.url" :appId="String(app.id)"
                :title="app.name" :headerColor="app.headerColor" :size="app.size || { w: 2, h: 2 }" :style="isEditMode ? {
                  animation: 'shake-edit ease .3s infinite !important',
                  transformOrigin: 'center !important',
                  perspective: '1000px !important',
                  willChange: 'transform !important',
                  backfaceVisibility: 'hidden !important',
                } : {}" />
              <div class="app-name">{{ app.name }}</div>
            </div>
          </div>
        </transition-group>

        <!-- 滑动切换分类提示 -->
        <div v-if="showSlideHint" class="slide-hint-container">
          <div class="slide-hint">
            {{ showSlideHint }}
          </div>
        </div>
      </div>
    </div>

    <!-- 底部Dock栏 - 在纯净模式下也显示，但没有应用时不显示 -->
    <DockBar :dock-apps="dockApps" @open-app="openApp" @show-context-menu.prevent.stop="showContextMenu" />

    <!-- 弹幕侧边栏 -->
    <!-- <DanmakuPanel v-show="!isMobile" /> -->
    <wheelSwitchingPage v-show="!isMobile" />

    <!-- 右键菜单 -->
    <ContextMenu :visible="showMenu" :position="menuPosition" :active-app="activeApp" :is-edit-mode="isEditMode"
      :dock-apps="dockApps" :is-dock-item="activeApp?.isDockItem || false" @close="hideContextMenu"
      @toggle-edit-mode="toggleEditMode" @add-icon-modal="showAddIconModal" @create-folder="createFolder"
      @set-app-size="setAppSize" @set-open-mode="setOpenMode" @move-app-to-category="moveAppToCategory"
      @delete-app="deleteApp" @add-to-dock="addToDock" @remove-from-dock="removeFromDock" @edit-icon="showEditIconModal"
      @change-wallpaper="wallpaperStore.refreshWallpaper" @toggle-pure-mode="togglePureMode" @fix-app="fixApp"
      @unfix-app="unfixApp" />

    <!-- 应用内打开弹窗 -->
    <Teleport to="body">
      <AppModal v-model:visible="appModalVisible" :url="activeAppUrl" :title="activeAppTitle" @close="closeAppModal" />
    </Teleport>

    <!-- 壁纸弹窗 -->
    <Teleport to="body">
      <WallPaperModal v-model:visible="wallModalVisible" @close="closeWallModal" />
    </Teleport>

    <!-- 添加自定义图标的模态窗口 -->
    <Teleport to="body">
      <IconModal v-model:visible="addIconModalVisible" :is-edit-mode="iconModalEditMode" :editing-icon="editingIcon"
        :current-category="activeCategory" @add-icon="handleAddIcon" @close="addIconModalVisible = false" @add-to-dock="addToDock" />
    </Teleport>

    <!-- 编辑图标的模态窗口 -->
    <Teleport to="body">
      <EditIconModal v-model:visible="editIconModalVisible" :editing-icon="editingIcon"
        @close="editIconModalVisible = false" @save-icon="handleSaveEditIcon" />
    </Teleport>
    <!-- 文件夹应用弹窗 -->
    <Teleport to="body">
      <FolderModal v-model:visible="folderModalVisible" :folder="activeFolder"
        :title="activeFolder ? activeFolder.name : '应用'" @close="closeFolderModal" @open-app="openAppFromFolder"
        @drag-start="handleDragStart" @add-app-to-folder="handleAddAppToFolder"
        @update-title="handleUpdateFolderTitle" @remove-app-from-folder="handleRemoveAppFromFolder"
        @add-app-to-desktop="handleAddAppToDesktop" />
    </Teleport>

    <!-- 应用合集弹窗 -->
    <Teleport to="body">
      <CollectionModal v-model:visible="collectionModalVisible" :collection="activeCollection"
        :current-category="activeCategory" :title="activeCollection ? activeCollection.name : '应用合集'"
        @close="closeCollectionModal" @open-app="openAppFromCollection" @add-icon="handleAddIcon"
        @add-to-dock="addToDock" @remove-from-dock="removeFromDock" />
    </Teleport>

  </div>
</template>


<script setup>
import { ref, onMounted, computed, watch, onUnmounted, nextTick, reactive } from 'vue'
import { useRouter } from 'vue-router'
import SearchEngine from '@/components/search/SearchEngine.vue'


import AppModal from '@/components/modal/AppModal.vue'
import WallPaperModal from '@/components/modal/wallPaperModal.vue'
import TimeDisplay from '@/components/home/<USER>'
import ContextMenu from '@/components/home/<USER>'
import DockBar from '@/components/home/<USER>'
import FolderModal from '@/components/modal/FolderModal.vue'
import IconModal from '@/components/modal/IconModal.vue'
import EditIconModal from '@/components/modal/EditIconModal.vue'
import CollectionCard from '@/components/home/<USER>'
import CollectionModal from '@/components/modal/CollectionModal.vue'
import DanmakuPanel from '@/components/home/<USER>'
import wheelSwitchingPage from '@/components/home/<USER>'
import Sortable from 'sortablejs'


// 特殊组件
import Weather from '@/components/weather/index.vue'  // 天气
import AnimatedClock from '@/components/time/AnimatedClock.vue' // 时钟
import WoodenFish from '@/components/woodenFish/WoodenFish.vue' // 木鱼
import RelaxCard from '@/components/relax/RelaxCard.vue' // 下班倒计时
import IframeCard from '@/components/iframe/IframeCard.vue' // 赛博喂仓鼠
import ImageCard from '@/components/iframe/ImageCard.vue' // 摸鱼日报
import LinkCard from '@/components/iframe/LinkCard.vue' // 内嵌网页
import VideoCardAdapter from '@/components/video/VideoCardAdapter.vue' // 摸鱼视频
import HotCard from '@/components/hotnet/hotcard.vue' // 实时新闻
import CalendarCard from '@/components/calendar/calendarCard.vue' // 日历
import TodoList from '@/components/todoList/todoList.vue'
import { useDeviceDetector } from '@/composables/useDeviceDetector'
import { useAppManager } from '@/composables/useAppManager'
import { useDragAndDrop } from '@/composables/useDragAndDrop'
import { useSwipeNavigation } from '@/composables/useSwipeNavigation'


// 导入localStorage清除脚本（仅用于修复布局问题，发布前可移除）
import '/localstorage-clear.js'
// 导入打工人语录
import workQuotes from '@/utils/workQuotes.json'
import emitter from '@/utils/mitt';

import {
  Modal,
  Tooltip,
  Input,
  Radio,
  Select,
  Button,
  Dropdown,
  Menu,
  Divider,
  message,
  notification
} from 'ant-design-vue'

import { getMenuList, getCollectList, getOfficeCollectList } from '@/api/navbar'
import { gsap } from 'gsap'
import { Flip } from 'gsap/Flip'
import vTextScale from '@/directives/textScale'

// 注册GSAP插件
gsap.registerPlugin(Flip)
import { useWallpaperStore } from '@/stores/wallpaper'
import { useUrlStore } from '../stores/url'
import { useNavigationStore } from '@/stores/navigation'
// 导入新的网格布局计算器
import { GridUtils, defaultCalculator } from '@/utils/gridLayoutCalculator'
import { getUserCollect, fixedApp, cancalfixedApp } from '@/api/collect'
import { folderGridCalculator } from '@/utils/folderGridCalculator'
import autoBackupManager from '@/utils/autoBackupManager'

import { saveFolderApp } from '@/api/folder'
import { deleteUserApp } from '@/api/navbar'
import { deleteFolder } from '@/api/folder'

import customAdd from '@/assets/icons/customAdd.svg'


// 导航存储
const navigationStore = useNavigationStore();
// 初始化 navigation store
// navigationStore.initNavigation()


// 壁纸存储
const wallpaperStore = useWallpaperStore()

// 路由实例
const router = useRouter()

// 当前激活的分类（使用 navigation store 的状态）
const activeCategory = computed(() => navigationStore.currentCategory)
// 存储所有分类的布局信息
const categoryLayoutsCache = ref({});
// 网格容器引用
const gridContainer = ref(null)
// 网格排序实例
let sortableInstance = null
// 计算container填充底部距离
const containerPaddingBottom = computed(() => {
  if (dockApps.value.length > 0) {
    return window.innerWidth <= 480 ? '60px' : window.innerWidth <= 768 ? '70px' : '80px'
  } else {
    return '20px'
  }
})

const screenWidth = window.screen.width;
const screenHeight = window.screen.height;



// 设备检测相关状态和方法（使用 useDeviceDetector composable）
const { isMobile, isTablet, isTouchDevice, isLandscape, detectDeviceType, shouldDisableDrag, detectOrientation } = useDeviceDetector()

// 应用管理相关状态和方法（使用 useAppManager composable）
const {
  defaultFolderIcon,
  confirmDeleteApp: confirmDeleteAppBase,
  createFolder: createFolderBase,
  checkDuplicateId: checkDuplicateIdBase,
  canAddAppToCategory: canAddAppToCategoryBase,
  handleAddIcon: handleAddIconBase
} = useAppManager()

// 创建包装函数来正确传递参数
function confirmDeleteApp(app) {
  confirmDeleteAppBase(app, navigationStore, categoryApps, saveAppOrder)
}

function createFolder() {
  createFolderBase(activeCategory, saveAppOrder)
}

function checkDuplicateId(id, categoryType = activeCategory.value) {
  return checkDuplicateIdBase(id, categoryType)
}

function canAddAppToCategory(newApp, categoryType = activeCategory.value) {
  return canAddAppToCategoryBase(newApp, categoryType)
}

function handleAddIcon(iconData) {
  handleAddIconBase(iconData, activeCategory, saveAppOrder)
}

// 拖拽和放置相关状态和方法（使用 useDragAndDrop composable）
const {
  draggedApp,
  isDraggingFromFolder,
  handleDragStart: handleDragStartBase,
  makeAppItemsDraggable: makeAppItemsDraggableBase,
  handleDropOnFolder: handleDropOnFolderBase
} = useDragAndDrop()

// 创建包装函数来正确传递参数
function handleDragStart(event, app, fromFolder = false) {
  handleDragStartBase(event, app, fromFolder, activeFolder)
}

function makeAppItemsDraggable() {
  makeAppItemsDraggableBase(navigationStore, shouldDisableDrag)
}

function handleDropOnFolder(appId, folderId, fromFolder, sourceFolderId) {
  handleDropOnFolderBase(appId, folderId, fromFolder, sourceFolderId)
}

// 滑动切换相关状态和方法（使用 useSwipeNavigation composable）
const {
  wheelCooldown,
  isChangingCategory,
  showSlideHint,
  lastScrollTime,
  SCROLL_COOLDOWN_TIME,
  switchToNextCategory: switchToNextCategoryBase,
  switchToPreviousCategory: switchToPreviousCategoryBase,
  showSlideHintMessage: showSlideHintMessageBase,
  scrollToTop: scrollToTopBase,
  handleTouchMove: handleTouchMoveBase,
  handleTouchEnd: handleTouchEndBase,
  cleanup: cleanupSwipeNavigation
} = useSwipeNavigation()

// 创建包装函数来正确传递参数
function switchToNextCategory() {
  switchToNextCategoryBase(currentMenuType, menuCategories, emitter)
}

function switchToPreviousCategory() {
  switchToPreviousCategoryBase(currentMenuType, menuCategories, emitter)
}

function showSlideHintMessage(message) {
  showSlideHintMessageBase(message)
}

function scrollToTop() {
  scrollToTopBase()
}

function handleTouchMove(e) {
  const result = handleTouchMoveBase(e, touchStartY, touchStartX)
  touchStartY = result.touchStartY
  touchStartX = result.touchStartX
}

function handleTouchEnd(e) {
  const result = handleTouchEndBase(e, touchStartY, touchStartX, wheelCooldown, isChangingCategory,
    switchToNextCategory, switchToPreviousCategory, currentMenuType, menuCategories)
  touchStartY = result.touchStartY
  touchStartX = result.touchStartX
}

// 处理防抖的窗口大小变化事件
function debouncedHandleResize() {
  // 检测设备类型
  detectDeviceType();
}

onUnmounted(() => {
  // 清理Sortable实例
  try {
    if (sortableInstance) {
      try {
        sortableInstance.destroy()
      } catch (error) {
        console.warn('卸载时销毁Sortable实例出错:', error)
      }
      sortableInstance = null
    }

    // 清理mitt事件监听器
    emitter.off('open-wallpaper-modal');
    emitter.off('login-success');
    emitter.off('isPureMode');
    emitter.off('changeModel');
    emitter.off('switchToMode');

    // 清理DOM元素上可能存在的Sortable实例
    if (gridContainer.value) {
      const appGrid = gridContainer.value.querySelector('.app-grid')
      if (appGrid && appGrid.sortableInstance) {
        try {
          appGrid.sortableInstance.destroy()
        } catch (e) {
          console.warn('清理DOM元素上的Sortable实例失败:', e)
        }
        appGrid.sortableInstance = null
      }
    }

    // 移除所有文件夹拖放的事件监听器
    document.querySelectorAll('.app-item[data-folder-drop-zone="true"]').forEach(item => {
      if (item._dragoverHandler) item.removeEventListener('dragover', item._dragoverHandler)
      if (item._dragleaveHandler) item.removeEventListener('dragleave', item._dragleaveHandler)
      if (item._dropHandler) item.removeEventListener('drop', item._dropHandler)
    })

    // 移除所有拖拽开始的事件监听器
    document.querySelectorAll('.app-item:not([data-is-folder="true"])').forEach(item => {
      if (item._dragStartHandler) item.removeEventListener('dragstart', item._dragStartHandler)
    })
  } catch (error) {
    console.warn('清理拖放相关资源时出错:', error)
  }

  // 移除事件监听器
  document.removeEventListener('click', handleClickOutside)
  // window.removeEventListener('resize', debouncedHandleResize)
  window.removeEventListener('menuTypeChanged', handleMenuTypeChanged)

  // 正确移除纯净模式切换事件监听器
  if (window._togglePureModeHandler) {
    window.removeEventListener('toggle-pure-mode', window._togglePureModeHandler)
    delete window._togglePureModeHandler
  }

  // 移除打开弹窗事件监听器
  window.removeEventListener('open-add-icon-modal', showAddIconModal)

  // 组件卸载时清理body类
  document.body.classList.remove('pure-mode-active')

  // 清理窗口大小变化监听器
  window.removeEventListener('resize', detectDeviceType)

  // 清理屏幕方向变化监听器
  if (window.screen && window.screen.orientation) {
    window.screen.orientation.removeEventListener('change', detectDeviceType)
  } else {
    window.removeEventListener('orientationchange', detectDeviceType)
  }

  // 清理dock栏数据更新监听器
  window.removeEventListener('dockAppsUpdated', () => { })

  // 移除布局设置变更事件监听器
  window.removeEventListener('layoutSettingChanged', handleLayoutSettingChanged)

  // 清理自动备份管理器
  autoBackupManager.cleanup()

  // 清理简化的滑动切换相关资源
  // 清理全局事件处理器
  if (window._globalWheelHandler) {
    window.removeEventListener('wheel', window._globalWheelHandler, { capture: true });
    delete window._globalWheelHandler;
  }
  if (window._globalTouchMoveHandler) {
    window.removeEventListener('touchmove', window._globalTouchMoveHandler, { capture: true });
    delete window._globalTouchMoveHandler;
  }
  if (window._globalTouchEndHandler) {
    window.removeEventListener('touchend', window._globalTouchEndHandler, { capture: true });
    delete window._globalTouchEndHandler;
  }

  // 清理触摸状态
  touchStartY = 0;
  touchStartX = 0;

  // 重置滚动时间
  lastScrollTime.value = 0;

  // 清理定时器
  cleanupSwipeNavigation()
})



// 网格列数响应式变量（支持异步加载）
const gridColumns = ref(12) // 默认值

// 智能网格信息展示
const gridInfo = ref(null)

// 异步初始化网格列数和样式
async function initGridColumns() {
  try {
    const params = await calculateOptimalGridParams()
    gridColumns.value = params.columns

    // 同时更新时间和搜索栏样式
    await updateComponentStyles()
  } catch (error) {
    gridColumns.value = 12
    // 错误时使用默认样式
    timeDisplayStyle.value = {}
    searchEngineStyle.value = {}
  }
}

// 更新组件样式
async function updateComponentStyles() {
  try {
    // 获取完整的网格计算结果
    const result = await GridUtils.quickCalculate({
      isMobile: isMobile.value
    })

    // 更新时间组件样式（边距）
    timeDisplayStyle.value = result.timeStyle || {}

    // 更新搜索栏样式
    searchEngineStyle.value = result.searchStyle || {}

    // 更新时间组件字体配置
    timeFontConfig.value = {
      fontSize: result.timeStyle?.['font-size'] || null,
      fontWeight: result.timeStyle?.['font-weight'] || null
    }

    console.log('组件样式更新完成:', {
      时间样式: result.timeStyle,
      搜索栏样式: result.searchStyle,
      时间字体配置: timeFontConfig.value
    })
  } catch (error) {
    console.warn('组件样式更新失败，使用默认样式:', error)
    timeDisplayStyle.value = {}
    searchEngineStyle.value = {}
    timeFontConfig.value = { fontSize: null, fontWeight: null }
  }
}

// 计算最佳网格参数 - 使用新的智能计算器（异步版本）
async function calculateOptimalGridParams() {
  // 平板设备专用16列计算逻辑
  if (isTablet.value) {
    return calculateTabletGridParams()
  }

  // 使用新的网格计算器，传入手机模式状态
  const result = await GridUtils.quickCalculate({
    isMobile: isMobile.value
  })

  // 转换为原有格式以保持兼容性
  const optimalParams = {
    columns: result.N,      // 列数
    iconSize: result.X1,    // 图标尺寸
    gap: result.X2,         // 间距尺寸
    gridWidth: result.W3,   // 网格总宽度
    windowRatio: result.P,  // 窗口占比
    gridRatio: result.Q     // 网格占比
  }

  // 不再进行缩放，固定显示1.00
  const scaleRatio = '1.00'
  const dpr = result.deviceInfo?.devicePixelRatio || window.devicePixelRatio || 1

  // 更新网格信息展示
  const gridInfoData = {
    设备类型: isMobile.value ? '手机' : isTablet.value ? '平板' : '桌面',
    桌面分辨率: `${result.W1}px`,
    浏览器宽度: `${result.W2}px`,
    窗口占比: `${(result.P * 100).toFixed(1)}%`,
    分辨率类型: result.resolutionName,
    列数: result.N,
    图标尺寸: `${result.X1}px`,
    缩放倍数: `${scaleRatio}x (DPR: ${dpr})`,
    真实尺寸: result.X1 + 'px',
    间距: `${result.X2}px`,
    网格宽度: `${result.W3}px`,
    网格占比: `${(result.Q * 100).toFixed(1)}%`,
    文字大小: `${result.fontSize}px`,
    行高: result.lineHeight,
    字体粗细: result.fontWeight,
    文字样式描述: result.fontStyleDescription,
    手机模式检测: `窗口宽度${result.W2}px ${result.W2 <= 480 ? '≤' : '>'} 480px`
  }
  // 更新响应式数据
  gridInfo.value = gridInfoData

  return optimalParams
}

// 平板设备专用网格参数计算（支持横竖屏）
function calculateTabletGridParams() {
  console.log('🔧 开始平板设备网格参数计算...')

  // 检测屏幕方向并设置对应的列数
  detectOrientation()
  const targetColumns = isLandscape.value ? 16 : 10 // 横屏16列，竖屏10列
  const gapRatio = 0.5 // 间隙比例，与默认配置保持一致

  console.log('📐 平板设备参数:', {
    屏幕方向: isLandscape.value ? '横屏' : '竖屏',
    目标列数: targetColumns,
    间隙比例: gapRatio
  })

  // 获取可用屏幕宽度（逻辑像素）
  const screenWidth = window.innerWidth
  const screenHeight = window.innerHeight

  console.log('📏 屏幕尺寸:', {
    宽度: screenWidth,
    高度: screenHeight,
    比例: (screenWidth / screenHeight).toFixed(2)
  })

  // 边界条件检查：确保屏幕宽度有效
  if (!screenWidth || screenWidth < 480) {
    console.warn('⚠️ 平板模式检测到异常屏幕宽度，回退到默认参数')
    return {
      columns: 6,
      iconSize: 40,
      gap: 20,
      gridWidth: 400,
      windowRatio: 0.8,
      gridRatio: 0.8
    }
  }

  const containerPadding = 40 // 预留左右边距
  const availableWidth = screenWidth - containerPadding

  // 反向计算图标尺寸：(可用宽度) / (列数 + (列数-1) * 间隙比例)
  const calculatedIconSize = Math.floor(availableWidth / (targetColumns + (targetColumns - 1) * gapRatio))

  console.log('🧮 图标尺寸计算:', {
    可用宽度: availableWidth,
    计算公式: `${availableWidth} / (${targetColumns} + ${targetColumns - 1} * ${gapRatio})`,
    计算结果: calculatedIconSize
  })

  // 设置图标尺寸边界限制
  const minIconSize = 20 // 最小图标尺寸
  const maxIconSize = 80 // 最大图标尺寸，避免过大

  // 应用边界限制
  let iconSize = Math.max(minIconSize, Math.min(maxIconSize, calculatedIconSize))

  // 额外检查：如果计算出的图标尺寸太小，适当调整列数
  if (calculatedIconSize < minIconSize) {
    console.warn(`⚠️ 平板${targetColumns}列模式图标过小(${calculatedIconSize}px)，保持最小尺寸${minIconSize}px`)
    iconSize = minIconSize
  }

  // 计算实际间隙尺寸
  const gap = Math.max(1, Math.floor(iconSize * gapRatio)) // 确保间隙至少为1px

  // 计算实际网格总宽度
  const gridWidth = iconSize * targetColumns + gap * (targetColumns - 1)

  // 计算网格占比，添加边界检查
  const gridRatio = availableWidth > 0 ? gridWidth / availableWidth : 1

  const result = {
    columns: targetColumns,
    iconSize: iconSize,
    gap: gap,
    gridWidth: gridWidth,
    windowRatio: availableWidth / screenWidth,
    gridRatio: gridRatio
  }

  console.log('✅ 平板设备网格参数计算完成:', result)

  return result
}

// 获取调用栈信息（增强版）
function getCallStack() {
  const stack = new Error().stack
  if (!stack) return '无法获取调用栈'

  // 过滤掉当前函数和一些内部函数
  const lines = stack.split('\n')
  const filteredLines = lines.filter((line, index) => {
    if (index === 0) return false // 跳过 "Error" 行
    if (line.includes('getCallStack')) return false // 跳过当前函数
    if (line.includes('parseGridColumns')) return false // 跳过解析函数
    if (line.includes('observeGridColumns')) return false // 跳过观察器函数
    if (line.includes('MutationObserver')) return false // 跳过观察器内部
    if (line.includes('Array.forEach')) return false // 跳过数组遍历
    return true
  }).slice(0, 10) // 增加到10层调用栈

  // 尝试解析函数名和位置
  const detailedStack = filteredLines.map(line => {
    // 尝试匹配不同的调用栈格式
    const patterns = [
      /at\s+(\w+)\s+\((.+):(\d+):(\d+)\)/, // at functionName (file:line:col)
      /at\s+(.+):(\d+):(\d+)/, // at file:line:col
      /(\w+)@(.+):(\d+):(\d+)/, // functionName@file:line:col
    ]

    for (const pattern of patterns) {
      const match = line.match(pattern)
      if (match) {
        if (match.length === 5) {
          return `${match[1]} (${match[2]}:${match[3]}:${match[4]})`
        } else if (match.length === 4) {
          return `${match[1]}:${match[2]}:${match[3]}`
        }
      }
    }

    return line.trim()
  })

  return detailedStack.join('\n')
}

// 解析 grid-template-columns 获取列数
function parseGridColumns(gridTemplateColumns) {
  if (!gridTemplateColumns || gridTemplateColumns === 'none') {
    return 0
  }

  // 计算 repeat() 函数中的列数
  const repeatMatch = gridTemplateColumns.match(/repeat\((\d+),/)
  if (repeatMatch) {
    return parseInt(repeatMatch[1])
  }

  // 计算空格分隔的列数
  const columns = gridTemplateColumns.split(' ').filter(col => col.trim() !== '')
  return columns.length
}

// 监听网格容器的 grid-template-columns 属性变化（增强版，包含调用栈追踪）
let gridObserver = null
const currentColumns = ref(0)
let gridChangeTimer = null

function observeGridColumns() {
  // 重新启用网格列数监听器，已修复右键菜单触发问题

  const gridContainer = document.querySelector('.app-grid')
  if (!gridContainer) {
    console.warn('未找到 .app-grid 元素，无法监听列数变化')
    return
  }

  // 清理之前的观察器
  if (gridObserver) {
    gridObserver.disconnect()
  }

  // 存储上一次的 grid-template-columns 值和时间戳
  let lastGridTemplateColumns = getComputedStyle(gridContainer).gridTemplateColumns
  let lastProcessTime = 0
  const DEBOUNCE_DELAY = 50 // 50ms 防抖延迟

  // 创建 MutationObserver 监听样式变化
  gridObserver = new MutationObserver((mutations) => {
    const now = Date.now()

    // 防抖：如果距离上次处理时间太短，跳过
    if (now - lastProcessTime < DEBOUNCE_DELAY) {
      return
    }

    mutations.forEach((mutation) => {
      if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
        const target = mutation.target
        const gridTemplateColumns = getComputedStyle(target).gridTemplateColumns

        // 只有当 grid-template-columns 真正发生变化时才处理
        if (gridTemplateColumns !== lastGridTemplateColumns) {
          lastProcessTime = now
          lastGridTemplateColumns = gridTemplateColumns

          // 解析列数
          const newColumns = parseGridColumns(gridTemplateColumns)

          // 🔍 详细调试：检查网格容器宽度变化和设备状态
          const containerRect = target.getBoundingClientRect()
          const windowWidth = window.innerWidth

          // 🔍 修复重复打印问题：只有列数真正发生变化时才触发
          if (newColumns !== currentColumns.value && currentColumns.value > 0 && !isChangingAppSize.value && !isContextMenuActive.value) {
            // 先更新列数，避免重复触发
            const oldColumns = currentColumns.value
            currentColumns.value = newColumns

            // 清除之前的定时器
            clearTimeout(gridChangeTimer)

            // 防抖处理，避免频繁触发
            gridChangeTimer = setTimeout(() => {
              // 获取调用栈
              const callStack = getCallStack()

              // 检查是否是应用尺寸变化或其他UI操作触发的变化
              const isAppSizeChange = callStack.includes('setAppSize') ||
                callStack.includes('size-changing') ||
                callStack.includes('FLIP') ||
                document.querySelector('.size-changing') !== null

              const isUIOperation = callStack.includes('hideContextMenu') ||
                callStack.includes('showContextMenu') ||
                callStack.includes('toggleEditMode') ||
                callStack.includes('moveAppToCategory') ||
                callStack.includes('setOpenMode') ||
                callStack.includes('deleteApp')

              // 检查是否包含右键菜单相关的调用
              const contextMenuRelated = callStack.includes('contextmenu') ||
                callStack.includes('ContextMenu') ||
                callStack.includes('handleClickOutside') ||
                callStack.includes('hideContextMenu') ||
                callStack.includes('showContextMenu')

              // 如果是应用尺寸变化或UI操作，只记录简单日志
              if (isAppSizeChange || isUIOperation) {
              } else if (!contextMenuRelated) {
                // 真正的网格布局变化，输出简化信息
                // 触发列数变化事件
                onColumnsChanged(newColumns, oldColumns, callStack)
              }
            }, 100) // 100ms 防抖延迟
          } else if (newColumns !== currentColumns.value) {
            // 如果列数变化但被标志位阻止，只更新列数不触发事件
            currentColumns.value = newColumns
          }
        }
      }
    })
  })

  // 开始观察
  gridObserver.observe(gridContainer, {
    attributes: true,
    attributeFilter: ['style']
  })

  // 初始化当前列数
  const initialGridTemplateColumns = getComputedStyle(gridContainer).gridTemplateColumns
  currentColumns.value = parseGridColumns(initialGridTemplateColumns)
}



// 列数变化处理函数（增强版，包含调用栈信息）
async function onColumnsChanged(newColumns, oldColumns, callStack = null) {
  // 简化日志，避免重复打印

  // 触发布局重新计算
  nextTick(() => {
    // 重新初始化Sortable以适应新的列数
    initSortable()
  })
}

const getLayoutInfo = () => {
  // 获取当前分类的应用数据
  const currentCategoryApps = getCurrentCategoryApps()
  // currentCategoryApps循环根据顺序加一个sort字段为它的顺序
  currentCategoryApps.forEach((app, index) => {
    app.sort = index;
  });


  // 将数据转换为字符串
  const content = JSON.stringify(currentCategoryApps, null, 2)

  // 创建Blob对象
  const blob = new Blob([content], { type: 'text/plain' })

  // 创建下载链接
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url

  // 设置文件名
  link.download = `${activeCategory.value}_layout.txt`

  // 触发下载
  document.body.appendChild(link)
  link.click()

  // 清理
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
  window.open('https://beian.miit.gov.cn', '_blank')
}



// 获取当前列数（用于拖拽等逻辑）
function getColumnsCount() {
  return gridColumns.value
}

// 根据文件夹项目数量计算能显示的项目数量（使用新的计算器）
function calculateFolderMaxItems(folder) {
  if (!folder || !folder.children) return 3 // 默认显示3个（1行）

  const itemCount = folder.children.length
  const folderSize = folder.size || { w: 2, h: 2 }

  // 调试输出

  // 使用新的文件夹网格计算器
  const containerWidth = 120
  const containerHeight = 120

  const layout = folderGridCalculator.calculateOptimalLayout(
    containerWidth,
    containerHeight,
    itemCount,
    folderSize  // 传递文件夹尺寸
  )


  return layout.maxItems
}

// 获取文件夹显示的子项目列表
function getFolderDisplayItems(folder) {
  if (!folder || !folder.children) return []

  const maxItems = calculateFolderMaxItems(folder)
  return folder.children.slice(0, maxItems)
}

// 获取文件夹网格的动态样式（使用新的文件夹网格计算器）
function getFolderGridStyle(folder) {
  const folderSize = folder?.size || { w: 2, h: 2 }

  if (!folder || !folder.children || folder.children.length === 0) {
    // 根据文件夹尺寸设置默认样式
    let widthPercent, heightPercent
    if (folderSize.w === 1 && folderSize.h === 1) {
      widthPercent = '70%'
      heightPercent = '70%'
    } else if (folderSize.h === 1) {
      widthPercent = '80%'
      heightPercent = '80%'
    } else {
      widthPercent = '85%'
      heightPercent = '85%'
    }

    // 3x1布局特殊处理：使用4列
    const columns = (folderSize.w === 3 && folderSize.h === 1) ? 4 : 3

    return {
      'display': 'grid',
      'grid-template-columns': `repeat(${columns}, 1fr)`,
      'grid-template-rows': 'repeat(1, 1fr)',
      'gap': '6px',
      'width': widthPercent,
      'height': heightPercent,
      'padding': '8px',
      'place-items': 'center',
      'place-content': 'start'
    }
  }

  const itemCount = folder.children.length

  // 使用新的文件夹网格计算器
  // 假设文件夹容器大小为 120x120px（可以根据实际情况调整）
  const containerWidth = 120
  const containerHeight = 120

  const layout = folderGridCalculator.calculateOptimalLayout(
    containerWidth,
    containerHeight,
    itemCount,
    folderSize  // 传递文件夹尺寸
  )

  return {
    'display': 'grid',
    'grid-template-columns': `repeat(${layout.columns}, 1fr)`,
    'grid-template-rows': `repeat(${layout.rows}, 1fr)`,
    'gap': `${layout.gap}px`,
    'width': layout.gridStyle.width,
    'height': layout.gridStyle.height,
    'padding': '8px',
    'place-items': 'center',
    'place-content': 'start',
    'box-sizing': 'border-box'
  }
}


// 本地引用，与 navigation store 保持同步
const allApps = computed(() => navigationStore.allApps)
const categoryApps = computed(() => navigationStore.categoryApps)

// 右键菜单相关
const showMenu = ref(false)
const menuPosition = ref({ top: '0px', left: '0px' })
const activeApp = ref(null)

// 注意：filteredApps 已移除，直接使用 navigationStore.currentApps

// 实际显示的应用列表，使用 navigation store 的数据
const displayedApps = computed(() => {
  // return
  const apps = navigationStore.currentApps
  if (navigationStore.currentCategory == 'collectNav') {
    apps.map((item) => {
      item.type = 'app'
    })
  }

  // 检查是否还有 collect 类型的应用
  const collectApps = apps.filter(app => app?.type === 'collect')
  if (collectApps.length > 0) {
    console.warn('发现未转换的 collect 类型应用:', collectApps)
    // 在这里进行最后的类型转换
    collectApps.forEach(app => {
      app.type = 'collection'
    })
  }

  // 在每个分类末尾添加"添加图标"应用，但收藏分类除外
  const appsWithAddIcon = [...apps]

  // 如果当前分类是收藏分类，不添加"添加图标"应用
  if (navigationStore.currentCategory === 'collectNav') {
    return appsWithAddIcon
  }

  // 创建"添加图标"应用对象
  const addIconApp = {
    id: `add-icon-${navigationStore.currentCategory}`,
    name: '添加应用',
    type: 'add-icon',
    icon: null, // 使用特殊图标
    color: '#f0f0f0',
    category: navigationStore.currentCategory,
    size: { w: 1, h: 1 },
    description: '点击添加新应用'
  }

  // 将"添加图标"应用添加到列表末尾
  appsWithAddIcon.push(addIconApp)

  return appsWithAddIcon
});



// 添加调试函数，用于检查应用数据结构
function debugAppData(apps, label = '应用数据') {


  // 检查collect类型是否存在
  const collectApps = apps.filter(app => app?.type === 'collect');
  if (collectApps.length > 0) {

  }

  // 检查collection类型
  const collectionApps = apps.filter(app => app.type === 'collection');
  if (collectionApps.length > 0) {

  }
}

// 更新显示的应用列表（现在主要用于重新初始化 UI）
function updateDisplayedApps() {
  // 注意：displayedApps 现在是计算属性，直接从 navigation store 获取数据
  // 这个函数主要用于重新初始化 UI 组件

  // 在下一个渲染周期确保DOM已更新
  nextTick(() => {
    // 重新初始化拖拽功能
    initSortable();
    makeAppItemsDraggable();
  });
}

// 注意：不再需要监听 filteredApps，因为 displayedApps 现在是计算属性

// 监听设备类型和屏幕方向变化，触发网格重新计算
watch([isMobile, isTablet, isLandscape, shouldDisableDrag], ([newMobile, newTablet, newLandscape, newDisableDrag], [oldMobile, oldTablet, oldLandscape, oldDisableDrag]) => {
  if (newMobile !== oldMobile || newTablet !== oldTablet || newLandscape !== oldLandscape || newDisableDrag !== oldDisableDrag) {

    // 重新计算网格参数
    initGridColumns()

    // 下一个渲染周期重新初始化
    nextTick(() => {
      initSortable()
    })
  }
});

// 当前菜单分类
const currentMenuType = ref('') // 初始为空，等待数据加载后设置
const menuCategories = ref([])
const allNavItems = ref([])
// 使用 Pinia store 来替代本地变量
const urlStore = useUrlStore();

// 同步数据到 navigation store（保持兼容性）
function syncToNavigationStore(category, apps) {
  // 直接更新 navigation store 的数据，不重复添加
  if (!navigationStore.categoryApps[category]) {
    navigationStore.categoryApps[category] = []
  }
  // 直接替换，而不是追加
  navigationStore.categoryApps[category] = [...apps]
  navigationStore.saveAllCategoryData()
}

// 辅助函数：获取当前分类的应用
function getCurrentCategoryApps() {
  const apps = navigationStore.categoryApps[activeCategory.value] || []
  // 过滤掉无效的应用元素和"添加图标"应用，确保数据完整性
  return apps.filter(app => app && app.id && app.type !== 'add-icon')
}

// 辅助函数：更新当前分类的应用
function updateCurrentCategoryApps(apps) {
  navigationStore.categoryApps[activeCategory.value] = apps
  syncToNavigationStore(activeCategory.value, apps)
}

// 清理重复数据的函数
function cleanupDuplicateApps() {
  try {
    const savedData = localStorage.getItem('categoryApps')
    if (!savedData) return

    const categoryData = JSON.parse(savedData)
    let hasChanges = false

    // 遍历每个分类，去除重复应用
    Object.keys(categoryData).forEach(category => {
      const apps = categoryData[category]
      if (!Array.isArray(apps)) return

      const uniqueApps = []
      const seenIds = new Set()

      apps.forEach(app => {
        if (!seenIds.has(app.id)) {
          seenIds.add(app.id)
          // 处理 collect 类型转换为 collection 类型
          if (app.type === 'collect') {
            app.type = 'collection'
            hasChanges = true
          }
          uniqueApps.push(app)
        } else {
          hasChanges = true
        }
      })

      categoryData[category] = uniqueApps
    })

    // 如果有变化，保存清理后的数据
    if (hasChanges) {
      localStorage.setItem('categoryApps', JSON.stringify(categoryData))

      // 同步到 navigation store
      Object.keys(categoryData).forEach(category => {
        navigationStore.categoryApps[category] = categoryData[category]
      })
    }
  } catch (error) {
    console.error('清理重复数据时出错:', error)
  }
}



// 切换菜单分类
function switchMenuType(type) {
  if (type === currentMenuType.value) return

  // 保存当前分类的布局信息
  saveCategoryLayout(currentMenuType.value)

  // 保存当前分类下的所有应用
  saveAppOrder()

  // 更新当前分类
  currentMenuType.value = type

  // 同时更新 navigation store 的当前分类
  navigationStore.switchCategory(type)

  // 注意：displayedApps 现在是计算属性，会自动响应 navigation store 的变化


  // 如果本地没有数据或恢复失败，再从API加载

  // 查找当前分类的应用
  const currentCategory = allNavItems.value.find(item => item.type === type)
  if (currentCategory && currentCategory.children) {
    // 转换数据格式
    const apps = currentCategory.children.map(child => {
      // 处理标准应用
      if (child.webType !== 'folder' && child.webType !== 'collection' && child.webType !== 'collect') {
        return {
          id: child.id,
          name: child.name,
          icon: child.logo,
          color: child.color || '#ffffff',
          category: type,
          size: {
            w: parseInt(child.w || 1),
            h: parseInt(child.h || 1)
          },
          x: parseInt(child.x),
          y: parseInt(child.y),
          url: child.websiteAddress ? `https://${child.websiteAddress}` : '',
          type: child.webType || 'app',
          description: child.descs || '',
          websiteAddress: child.websiteAddress,
          iscanopen: child.iscanopen
        }
      }
      // 处理文件夹和应用合集
      else {
        // 添加调试日志

        // 直接检查是否为collect类型
        if (child.webType === 'collect') {

          // 处理多层嵌套的情况
          if (child.children && child.children.some(item => item.webType === 'collect')) {

            // 展平嵌套合集，提取所有子项
            let allItems = [];
            child.children.forEach(subItem => {
              if (subItem.webType === 'collect' && subItem.children) {
                // 将嵌套合集的子项添加到主合集
                allItems = allItems.concat(subItem.children);
              } else {
                // 普通子项直接添加
                allItems.push(subItem);
              }
            });

            // 用展平后的子项替换原始子项列表
            child.children = allItems;
          }
        }

        // 为文件夹或合集设置特殊图标和子项
        const folderChildren = Array.isArray(child.children) ?
          child.children.map(subChild => {
            // 打印子项原始数据

            return {
              id: subChild.id,
              name: subChild.name,
              icon: subChild.logo, // 图标使用logo字段
              color: subChild.color || '#ffffff',
              category: type,
              size: {
                w: parseInt(subChild.w || 1),
                h: parseInt(subChild.h || 1)
              },
              x: parseInt(subChild.x || 0),
              y: parseInt(subChild.y || 0),
              url: subChild.websiteAddress ? `https://${subChild.websiteAddress}` : '',
              type: subChild.webType || 'app',
              description: subChild.descs || '',
              websiteAddress: subChild.websiteAddress, // 确保websiteAddress属性被正确传递
              iscanopen: subChild.iscanopen
            };
          }) : []

        return {
          // 使用稳定的ID，确保唯一性
          id: (child.webType === 'collection' || child.webType === 'collect')
            ? `collection-${child.id || `${child.name}-${Math.round(Math.random() * 10000)}`}`
            : `folder-${child.id || `${child.name}-${Math.round(Math.random() * 10000)}`}`,
          name: child.type || child.name || ((child.webType === 'collection' || child.webType === 'collect') ? '应用合集' : '文件夹'),
          icon: child.logo || child.icon || ((child.webType === 'collection' || child.webType === 'collect') ? '📦' : '📁'),
          color: child.color || '#ffffff',
          category: type,
          size: {
            w: parseInt(child.w || 2),
            h: parseInt(child.h || 2)
          },
          x: parseInt(child.x || 0),
          y: parseInt(child.y || 0),
          // 根据webType区分folder和collection
          type: child.webType === 'collect' ? 'collection' : (child.webType || 'folder'),
          children: folderChildren,
          description: `包含${folderChildren.length}个应用`,
          // 保留原始ID以便于追踪
          originalId: child.id,
          // 直接传递websiteAddress属性
          websiteAddress: child.websiteAddress,
          iscanopen: child.iscanopen
        }
      }
    })

    // 查找属于当前分类的特殊卡片 - 使用永久备份
    const specialCards = specialCardsMaster.value.filter(app => app.category === type)


    // 处理数据中可能存在的'collect'类型
    const processedApps = apps.map(app => {
      if (app.type === 'collect') {
        return { ...app, type: 'collection' };
      }
      return app;
    });

    // 确保所有collect类型已转换为collection
    processedApps.forEach(app => {
      if (app.type === 'collect') {
        console.error('类型转换失败:', app);
      }
    });

    // 合并API数据和特殊卡片
    const mergedApps = [...processedApps, ...specialCards]

    // 添加详细调试
    debugAppData(mergedApps, '合并后的应用');

    // 更新 navigation store 的应用列表
    navigationStore.setAllApps(mergedApps)

    // 从保存的布局中恢复当前分类的布局，而不是重新初始化
    restoreCategoryLayout(type)

    nextTick(() => {
      initSortable()
      reapplyCardAnimations()
      // 确保拖拽功能已初始化
      ensureSortableInitialized()
      // 重新初始化滑动切换功能
      initSwipeNavigation()
    })
  }
  nextTick(() => {
    initSortable()
  })

}

// 保存当前分类的布局信息
function saveCategoryLayout(category) {
  if (!category || category === 'all') return

  // 获取当前显示的应用布局信息
  const layoutInfo = displayedApps.value.map(app => {
    // 仅保存必要的布局信息：id, x, y, w, h
    return {
      id: app.id,
      x: app.x || 0,
      y: app.y || 0,
      size: {
        w: app.size?.w || 1,
        h: app.size?.h || 1
      }
    }
  })

  // 保存当前分类的布局到内存缓存 - 使用.value访问ref对象
  categoryLayoutsCache.value[category] = layoutInfo

  // 同时保存到localStorage做持久化
  try {
    localStorage.setItem('navBarCategoryLayouts', JSON.stringify(categoryLayoutsCache.value))
  } catch (e) {
    console.error('保存布局信息到localStorage失败', e)
  }

  // 不要在这里立即重新应用布局，仅保存信息
}

// 恢复指定分类的布局信息
function restoreCategoryLayout(category) {
  // 保存原始的allApps，以便合并本地应用和API应用
  const originalAllApps = [...navigationStore.allApps];

  // 优先从 navigation store 获取数据
  let savedCategoryApps = navigationStore.categoryApps[category];

  // 如果 store 中没有数据，尝试从统一的 localStorage 获取
  if (!savedCategoryApps || savedCategoryApps.length === 0) {
    try {
      const allCategoryApps = localStorage.getItem('categoryApps');
      if (allCategoryApps) {
        const parsed = JSON.parse(allCategoryApps);
        if (parsed[category] && parsed[category].length > 0) {
          savedCategoryApps = parsed[category];
          // 同步到 store
          navigationStore.categoryApps[category] = savedCategoryApps;
        }
      }
    } catch (e) {
      console.error(`从统一存储获取${category}分类失败`, e);
    }
  }

  // 兼容旧的分散存储（迁移用）
  let savedLayoutApps = null;
  if (!savedCategoryApps || savedCategoryApps.length === 0) {
    try {
      const savedLayout = localStorage.getItem(`layout_${category}`);
      if (savedLayout) {
        savedLayoutApps = JSON.parse(savedLayout);

        // 迁移到新的统一存储
        navigationStore.categoryApps[category] = savedLayoutApps;
        navigationStore.saveAllCategoryData();
        localStorage.removeItem(`layout_${category}`);

        savedCategoryApps = savedLayoutApps;
      }
    } catch (e) {
      console.error(`解析${category}分类旧存储数据失败`, e);
    }
  }

  // 先尝试从内存中读取布局信息 - 使用.value访问ref对象
  let layoutInfo = categoryLayoutsCache.value[category];

  // 如果内存中没有，尝试从localStorage加载所有布局
  if (!layoutInfo && typeof localStorage !== 'undefined') {
    try {
      const savedLayouts = localStorage.getItem('navBarCategoryLayouts');
      if (savedLayouts) {
        const parsed = JSON.parse(savedLayouts);
        // 使用.value访问ref对象
        Object.assign(categoryLayoutsCache.value, parsed);
        layoutInfo = categoryLayoutsCache.value[category];
      }
    } catch (e) {
      console.error('从localStorage加载布局信息失败', e);
    }
  }

  // 特殊卡片始终保持
  const specialCards = specialCardsMaster.value.filter(app =>
    app.category === category
  );

  // 决定使用哪个应用列表
  let appsToUse = [];

  // 优先使用统一存储的分类应用数据
  if (savedCategoryApps && savedCategoryApps.length > 0) {
    appsToUse = savedCategoryApps;
  }
  // 最后使用API获取的应用
  else {
    appsToUse = originalAllApps.filter(app => app.category === category);
  }

  // 始终确保特殊卡片存在，并且位于列表前面
  const specialCardIds = specialCards.map(card => card.id);
  const regularApps = appsToUse.filter(app => !specialCardIds.includes(app.id));

  // 处理类型转换并合并特殊卡片和常规应用
  regularApps.forEach(app => {
    // 处理 collect 类型转换为 collection 类型
    if (app.type === 'collect') {
      app.type = 'collection'
    }
  })

  const mergedApps = [...specialCards, ...regularApps];

  // 如果找到了布局信息，将其应用到应用列表
  if (layoutInfo && layoutInfo.length > 0) {

    // 遍历应用列表，应用布局信息
    mergedApps.forEach(app => {
      const savedLayout = layoutInfo.find(item => item.id === app.id);
      if (savedLayout) {
        app.x = savedLayout.x;
        app.y = savedLayout.y;
        if (savedLayout.size) {
          app.size = savedLayout.size;
        }
      }
    });
  }


  // 更新分类应用列表（通过 navigation store）
  navigationStore.categoryApps[category] = mergedApps;
  // 同步到 navigation store
  syncToNavigationStore(category, mergedApps);

  // 更新全局应用列表，确保所有本地添加的应用也在全局列表中
  const allAppsSet = new Set(navigationStore.allApps.map(app => app.id));
  mergedApps.forEach(app => {
    if (!allAppsSet.has(app.id)) {
      navigationStore.allApps.push(app);
    }
  });

  // 恢复成功，返回true
  return true;
}

const changeDockerBar = () => {
  const token = localStorage.getItem('token')
  if (token) {
    getUserCollect().then((res) => {
      if (res.status == 200) {
        // 获取现有的系统默认数据
        const existingEntertainmentData = JSON.parse(localStorage.getItem('homeDockApps') || '[]')
        const existingOfficeData = JSON.parse(localStorage.getItem('officeDockApps') || '[]')

        // 分别处理娱乐模式和办公模式的用户收藏数据
        const userEntertainmentApps = []
        const userOfficeApps = []

        // 按 menuhometype 分离用户收藏数据
        res.data.forEach(item => {
          const appData = {
            id: item.id,
            name: item.name,
            icon: item.logo,
            color: item.color,
            description: item.descs,
            url: item.websiteAddress,
            websiteAddress: item.websiteAddress,
            iscanopen: item.iscanopen,
            isfixed: item.isfixed
          }

          if (item.menuhometype == 'MO_YU') {
            userEntertainmentApps.push(appData)
          } else if (item.menuhometype == 'BAN_GONG') {
            userOfficeApps.push(appData)
          }
        })

        // 智能合并数据：系统默认数据 + 用户个人数据
        const mergeAppsData = (systemApps, userApps) => {
          // 创建用户应用ID的Set，用于快速查找
          const userAppIds = new Set(userApps.map(app => app.id))

          // 保留系统数据中用户没有的应用
          const filteredSystemApps = systemApps.filter(app => !userAppIds.has(app.id))

          // 合并数据：系统应用 + 用户应用
          const mergedApps = [...filteredSystemApps, ...userApps]

          // 按固定状态排序：固定应用(isfixed=1)在前，其他在后
          return mergedApps.sort((a, b) => {
            if (a.isfixed === 1 && b.isfixed !== 1) return -1
            if (a.isfixed !== 1 && b.isfixed === 1) return 1
            return 0
          })
        }

        const finalEntertainmentApps = mergeAppsData(existingEntertainmentData, userEntertainmentApps)
        const finalOfficeApps = mergeAppsData(existingOfficeData, userOfficeApps)

        // 分别存储到对应的localStorage
        localStorage.setItem('homeDockApps', JSON.stringify(finalEntertainmentApps))
        localStorage.setItem('officeDockApps', JSON.stringify(finalOfficeApps))

        // 根据当前数据源更新显示的dock栏数据
        if (navigationStore.currentDataSource == 'entertainment') {
          dockApps.value = finalEntertainmentApps
        } else if (navigationStore.currentDataSource == 'office') {
          dockApps.value = finalOfficeApps
        }

        // 触发Navigation Store更新收藏数据
        navigationStore.refreshCollectAppsData()

        // 触发dock栏数据更新事件，通知其他组件
        window.dispatchEvent(new CustomEvent('dockAppsUpdated', {
          detail: {
            dataSource: navigationStore.currentDataSource,
            storageKey: navigationStore.currentDataSource === 'office' ? 'officeDockApps' : 'homeDockApps',
            data: dockApps.value
          }
        }))
      }
    })
  }
}


// 监听菜单类型变化
watch(currentMenuType, () => {
  // 同步更新分类
  activeCategory.value = currentMenuType.value
})

// 时间和搜索栏样式（将通过网格布局计算器获取）
const timeDisplayStyle = ref({})
const searchEngineStyle = ref({})

// 时间组件的字体配置
const timeFontConfig = ref({
  fontSize: null,
  fontWeight: null
})

// 初次挂载时更新显示列表
onMounted(async () => {
  // 首先清理重复数据
  cleanupDuplicateApps()

  // 确保设备检测完成后再初始化网格布局
  console.log('🔍 开始设备检测和网格初始化...')

  // 强制执行设备检测
  detectDeviceType()

  // 等待一个渲染周期确保设备检测完成
  await nextTick()

  console.log('📱 设备检测结果:', {
    isMobile: isMobile.value,
    isTablet: isTablet.value,
    isLandscape: isLandscape.value,
    shouldDisableDrag: shouldDisableDrag.value
  })

  // 初始化智能网格布局
  await initGridColumns();

  emitter.on('login-success', (event) => {
    changeDockerBar();
    // 用户登录成功后自动还原备份
    autoBackupManager.autoRestore();
  });

  emitter.on('isPureMode', (event) => {
    togglePureMode()
  })


  emitter.on('changeModel', (event) => {
    switchDataSource(event.message)
  })

  // 监听轮盘的直接模式切换事件
  emitter.on('switchToMode', (event) => {
    switchToTargetMode(event.targetMode)
  })

  // 监听打开壁纸弹窗事件
  emitter.on('open-wallpaper-modal', () => {
    wallModalVisible.value = true
    wallpaperStore.fetchOfficialWallpapers()
  })


  // 设置智能网格响应式监听
  const cleanupGridWatcher = GridUtils.watchResize((result) => {
    // 触发Vue的响应式更新
    nextTick(() => {
      initSortable()
    })
  }, 150, true) // 150ms防抖，启用自动文字样式应用

  // 在组件卸载时清理监听器
  onUnmounted(() => {
    if (cleanupGridWatcher) {
      cleanupGridWatcher()
    }
  })



  // 检查是否需要进入纯净模式（来自路由 /lite）
  const shouldEnterPureMode = localStorage.getItem('shouldEnterPureMode')
  if (shouldEnterPureMode === 'true') {
    // 清除标记
    localStorage.removeItem('shouldEnterPureMode')
    // 在下一个渲染周期激活纯净模式，确保组件完全挂载
    nextTick(() => {
      togglePureMode()
    })
  }



  // 监听 navigation store 的分类变化
  watch(() => navigationStore.currentCategory, (newCategory, oldCategory) => {
    if (newCategory !== oldCategory) {
      switchMenuType(newCategory)
    }
  })

  // 添加窗口大小变化监听，使用防抖函数
  window.addEventListener('resize', debouncedHandleResize)

  // 监听菜单类型变化事件
  window.addEventListener('menuTypeChanged', handleMenuTypeChanged)

  // 添加点击外部关闭菜单和编辑模式的监听器
  document.addEventListener('click', handleClickOutside)

  // 监听布局设置变更事件
  window.addEventListener('layoutSettingChanged', handleLayoutSettingChanged)


  // 清除过渡动画类
  document.body.classList.remove('office-transition')
  document.body.classList.remove('home-transition')

  // 创建事件处理函数的引用以便正确移除
  const handleTogglePureMode = (event) => {
    // 添加过渡动画类
    document.body.classList.add('office-transition')

    // 显示加载状态
    // message.loading({ content: '正在切换到办公模式...', duration: 1 })

    // 延迟执行路由跳转，确保动画有时间显示
    setTimeout(() => {
      document.body.classList.remove('office-transition')
      // 使用Vue Router导航而不是直接修改URL
      router.push('/office')

    }, 600)
  }

  // 监听HeaderBar的纯净模式切换事件
  window.addEventListener('toggle-pure-mode', handleTogglePureMode)

  // 保存事件处理函数引用到组件实例上
  window._togglePureModeHandler = handleTogglePureMode

  // 添加监听顶部HeaderBar触发的打开弹窗事件
  window.addEventListener('open-add-icon-modal', showAddIconModal)

  // 加快Sortable初始化速度
  nextTick(() => {
    // 直接初始化，减少延迟
    initSortable();
    ensureSortableInitialized();
    // 确保文件夹拖拽功能初始化
    makeAppItemsDraggable();

    // 在Sortable初始化后再初始化滑动功能，确保DOM已准备好
    setTimeout(() => {
      initSwipeNavigation();

      // 启动网格列数监听器
      observeGridColumns();
    }, 500);
  });

  // 随机获取一条打工人语录
  const quotes = workQuotes.quotes;
  const randomQuote = quotes[Math.floor(Math.random() * quotes.length)];

  // notification.info({
  //   message: '打工人语录',
  //   description: randomQuote,
  //   duration: 10,
  // });
  // 初始化布局缓存
  try {
    const savedLayouts = localStorage.getItem('navBarCategoryLayouts')
    if (savedLayouts) {
      // 使用.value访问ref对象
      Object.assign(categoryLayoutsCache.value, JSON.parse(savedLayouts))
    }
  } catch (e) {
    console.error('加载保存的布局信息失败', e)
  }

  // 初始化dock栏数据
  initDockApps()
  // 监听窗口变化，动态更新设备类型和屏幕方向
  window.addEventListener('resize', detectDeviceType)

  // 监听屏幕方向变化（兼容不同浏览器）
  if (window.screen && window.screen.orientation) {
    window.screen.orientation.addEventListener('change', detectDeviceType)
  } else {
    // 备用方案：监听orientationchange事件
    window.addEventListener('orientationchange', () => {
      // 延迟执行，确保屏幕尺寸已更新
      setTimeout(detectDeviceType, 100)
    })
  }

  // 组件挂载后立即检查一次，确保首次渲染正确
  detectDeviceType()

  // 监听Navigation Store的dock栏数据更新事件
  window.addEventListener('dockAppsUpdated', (event) => {
    const { dataSource, data } = event.detail

    // 如果是当前数据源的更新，同步到Home.vue的dockApps
    if (dataSource === navigationStore.currentDataSource) {
      dockApps.value = data
    }
  })

  // 初始化滑动切换分类功能不再在这里调用，移到nextTick后 

  // 将测试函数暴露到全局，方便在控制台调用
  if (typeof window !== 'undefined') {
    window.debugDeviceInfo = GridUtils.debugDeviceInfo
    window.debugApiConfig = GridUtils.debugApiConfig
    // 暴露设备检测函数，方便调试
    window.testDeviceDetection = () => {
      detectDeviceType()
      return {
        isMobile: isMobile.value,
        isTablet: isTablet.value,
        isLandscape: isLandscape.value,
        isTouchDevice: isTouchDevice.value,
        shouldDisableDrag: shouldDisableDrag.value,
        userAgent: navigator.userAgent,
        screenSize: `${window.innerWidth}x${window.innerHeight}`,
        orientation: isLandscape.value ? '横屏' : '竖屏'
      }
    }
  }
})

// 处理菜单类型变化事件
function handleMenuTypeChanged(event) {
  if (event.detail && event.detail.type) {
    switchMenuType(event.detail.type)
    // 触发自定义事件，通知侧边栏更新菜单 
    window.dispatchEvent(new CustomEvent('menuCategoriesChanged', {
      detail: {
        categories: menuCategories.value,
        activeType: event.detail.type
      }
    }))
    // 确保在分类切换完成后重新初始化滚动功能
    setTimeout(() => {
      initSwipeNavigation();
    }, 500);
  }
}


// 根据卡片类型获取对应的组件
function getCardComponent(type) {
  // 组件映射表，可以根据需要扩展
  const componentMap = {
    'weather': Weather,
    'clock': AnimatedClock,
    'woodenFish': WoodenFish,
    'RelaxCard': RelaxCard, // 添加摸鱼卡片组件
    'iframe': IframeCard,
    'ImageCard': ImageCard,
    'linkCard': LinkCard,
    'video': VideoCardAdapter, // 添加视频卡片组件
    'HotCard': HotCard, // 添加热榜卡片组件
    // 可以添加更多组件映射
  }

  // 返回对应的组件，如果没有找到则返回一个占位符组件
  return componentMap[type] || {
    template: `<div class="placeholder-card">未知卡片类型: ${type}</div>`
  }
}

// 根据组件名称字符串获取对应的组件引用
function getComponentByName(componentName) {
  // 组件映射表，将组件名字符串映射到导入的组件
  const componentsMap = {
    'VideoCardAdapter': VideoCardAdapter,
    'Weather': Weather,
    'AnimatedClock': AnimatedClock,
    'WoodenFish': WoodenFish,
    'RelaxCard': RelaxCard,
    'IframeCard': IframeCard,
    'ImageCard': ImageCard,
    'LinkCard': LinkCard,
    'HotCard': HotCard,
    'CalendarCard': CalendarCard,
    'TodoList': TodoList
    // 可以根据需要添加更多映射
  }

  // 返回对应的组件引用，如果没有找到则返回一个占位符组件
  return componentsMap[componentName] || {
    template: `<div class="placeholder-card">未找到组件: ${componentName}</div>`
  }
}

// 保存应用顺序到本地存储
function saveAppOrder() {
  // 更新当前分类的应用数据到 navigation store
  const currentApps = getCurrentCategoryApps()
  if (activeCategory.value && currentApps.length > 0) {
    // 更新 navigation store 中的数据
    navigationStore.categoryApps[activeCategory.value] = currentApps

    // 记录保存时合集的数量和子项数量，用于调试
    const collections = currentApps.filter(app => app.type === 'collection');

    // 同时更新布局缓存，但不触发DOM更新
    if (activeCategory.value !== 'all') {
      // 只保存布局信息，不重新应用布局
      const layoutInfo = currentApps.map(app => ({
        id: app.id,
        x: app.x || 0,
        y: app.y || 0,
        size: {
          w: app.size?.w || 1,
          h: app.size?.h || 1
        }
      }));

      // 更新内存缓存
      categoryLayoutsCache.value[activeCategory.value] = layoutInfo;

      // 保存到localStorage
      try {
        localStorage.setItem('navBarCategoryLayouts', JSON.stringify(categoryLayoutsCache.value));
      } catch (e) {
        console.error('保存布局信息到localStorage失败', e);
      }
    }
  }

  // 统一保存所有分类的应用数据
  navigationStore.saveAllCategoryData();

  // 触发自动备份
  autoBackupManager.triggerAutoBackup();
}

// 初始化Sortable实例
async function initSortable() {
  try {
    // 清理现有实例
    if (sortableInstance) {
      try {
        sortableInstance.destroy()
      } catch (error) {
        console.warn('销毁Sortable实例时出错:', error)
      }
      sortableInstance = null
    }
    // 确保在DOM渲染后再执行
    nextTick(async () => {
      try {
        if (!gridContainer.value) return
        const appGrid = gridContainer.value.querySelector('.app-grid')
        // 只有appGrid存在且有子元素时才初始化
        if (!appGrid || appGrid.children.length === 0) {
          // 如果容器尺寸无效或没有子元素，延迟初始化
          setTimeout(() => {
            initSortable()
          }, 200)
          return
        }
        // 获取最新的自适应参数
        const optimalParams = await calculateOptimalGridParams()
        const columns = optimalParams.columns
        const iconSize = optimalParams.iconSize
        const gap = optimalParams.gap
        // 平板设备使用动态列数（横屏16列，竖屏10列），其他设备使用计算出的列数
        const tabletColumns = isTablet.value ? optimalParams.columns : columns
        const finalMaxWidth = isTablet.value ? optimalParams.gridWidth : (columns * iconSize + (columns - 1) * gap)

        // 平板设备专用样式应用逻辑
        if (isTablet.value) {
          console.log('📱 应用平板设备网格样式:', {
            列数: tabletColumns,
            图标尺寸: iconSize,
            间距: gap,
            最大宽度: finalMaxWidth
          })
          appGrid.style.gridTemplateColumns = `repeat(${tabletColumns}, ${iconSize}px)`
        } else if (isMobile.value) {
          console.log('📱 应用手机设备网格样式:', {
            列数: 6,
            图标尺寸: iconSize,
            间距: gap
          })
          appGrid.style.gridTemplateColumns = `repeat(6, ${iconSize}px)`
        } else {
          console.log('🖥️ 应用桌面设备网格样式:', {
            列数: columns,
            图标尺寸: iconSize,
            间距: gap
          })
          appGrid.style.gridTemplateColumns = `repeat(${columns}, ${iconSize}px)`
        }

        appGrid.style.gridAutoRows = `${iconSize}px`  // 修复：添加grid-auto-rows
        appGrid.style.maxWidth = `${finalMaxWidth}px`
        appGrid.style.gap = `${gap}px`

        // 创建Sortable实例前先检查appGrid是否已经有Sortable实例
        if (appGrid.sortableInstance) {
          try {
            appGrid.sortableInstance.destroy()
          } catch (e) {
            console.warn('清理已存在的Sortable实例失败:', e)
          }
        }

        // 创建Sortable实例
        sortableInstance = Sortable.create(appGrid, {
          animation: 300, // 减少动画时间提高流畅度
          disabled: shouldDisableDrag.value, // 手机和平板禁用拖动排序
          filter: '.add-icon-item', // 排除"添加图标"应用参与排序
          preventOnFilter: false, // 允许在过滤元素上触发其他事件
          onEnd(evt) {
            if (evt && evt.oldIndex !== undefined && evt.newIndex !== undefined) {
              updateAppPositions(evt)
            }
          },
          onUpdate(evt) {
            if (evt && evt.oldIndex !== undefined && evt.newIndex !== undefined) {
              updateAppPositions(evt)
            }
          },
          // 添加错误处理
          onMove(evt, originalEvent) {
            try {
              // 禁止移动"添加图标"应用
              if (evt.related && evt.related.classList.contains('add-icon-item')) {
                return false
              }
              return !shouldDisableDrag.value // 手机和平板返回false禁止移动
            } catch (error) {
              console.warn('拖动移动错误:', error)
              return false // 禁止移动以防止错误
            }
          }
        })

        // 保存引用到DOM元素上，便于清理
        appGrid.sortableInstance = sortableInstance

        // 添加文件夹拖放支持，仅在非触摸设备上启用
        nextTick(() => {
          try {
            if (!shouldDisableDrag.value) {
              initDragAndDrop()
            }
          } catch (error) {
            console.warn('初始化拖放功能出错:', error)
          }
        })
      } catch (error) {
        console.error('初始化Sortable时出错:', error)
      }
    })
  } catch (error) {
    console.error('Sortable初始化失败:', error)
  }
}

// 更新应用位置
function updateAppPositions(evt) {

  // return
  if (activeCategory.value === 'all') return

  const { oldIndex, newIndex } = evt
  if (oldIndex === newIndex) return

  // 获取当前分类的应用列表，过滤掉无效元素和"添加图标"应用
  const apps = [...getCurrentCategoryApps()].filter(app => app && app.id && app.type !== 'add-icon')

  // 验证索引有效性
  if (oldIndex < 0 || oldIndex >= apps.length || newIndex < 0 || newIndex > apps.length) {
    console.warn('拖拽索引超出范围:', { oldIndex, newIndex, appsLength: apps.length })
    return
  }

  // 移动应用
  const [movedApp] = apps.splice(oldIndex, 1)

  // 验证移动的应用是否有效
  if (!movedApp || !movedApp.id) {
    console.warn('拖拽的应用无效:', movedApp)
    return
  }

  apps.splice(newIndex, 0, movedApp)

  // 更新应用列表
  updateCurrentCategoryApps(apps)

  // 直接获取DOM中的实际顺序
  const appItems = document.querySelectorAll('.app-item')

  if (appItems && appItems.length > 0) {
    // 创建一个新的应用数组，按照DOM的实际顺序
    const orderedApps = []

    // 按DOM顺序获取所有应用元素
    appItems.forEach(item => {
      const appId = item.getAttribute('data-id')
      if (!appId) return

      // 找到对应的应用对象，添加安全检查
      const app = apps.find(a => a && a.id && a.id.toString() === appId)
      if (!app) {
        console.warn('找不到对应的应用对象:', { appId, availableApps: apps.map(a => a ? a.id : 'undefined') })
        return
      }

      // 获取元素的网格位置
      const computedStyle = window.getComputedStyle(item)
      const gridColumnStart = parseInt(computedStyle.gridColumnStart || '1')
      const gridRowStart = parseInt(computedStyle.gridRowStart || '1')

      // 更新坐标
      app.x = (gridColumnStart - 1).toString()
      app.y = (gridRowStart - 1).toString()

      // 添加到有序数组，保持DOM的实际顺序
      orderedApps.push(app)
    })

    // 直接使用DOM中的顺序，不做额外排序
    if (orderedApps.length > 0) {
      updateCurrentCategoryApps(orderedApps)

      // 保存更新后的应用顺序和位置信息
      saveAppOrder()
    }
  }
}

// 重新应用卡片动画效果，在切换分类或动画设置更改时调用
function reapplyCardAnimations() {
  // 等待DOM更新
  nextTick(() => {
    const appItems = document.querySelectorAll('.app-item');
    if (appItems && appItems.length > 0) {
      // 首先移除所有动画类，然后重新应用
      appItems.forEach(item => {
        // 保存原有的类列表
        // const classList = Array.from(item.classList).filter(cls => !cls.startsWith('card-animate-'));

        // 暂时移除所有动画类以重置动画
        // item.className = classList.join(' ');

        // 强制重排
        void item.offsetWidth;

        // 确保动画完成后清除状态
        item.addEventListener('animationend', function resetAnimationState() {
          item.style.animation = '';
          item.removeEventListener('animationend', resetAnimationState);
        }, { once: true });
      });
    }
  });
}

// 检查应用是否应该显示（搜索过滤）
function isAppVisible(app) {
  if (!searchQuery.value) return true
  const query = searchQuery.value.toLowerCase()
  return app.name.toLowerCase().includes(query)
}

// 搜索功能
const searchQuery = ref('')

// 更新searchQuery - 从SearchEngine组件接收搜索查询
function updateSearchQuery(query) {
  searchQuery.value = query
}



// 显示上下文菜单
function showContextMenu(event, app, isDockItem = false) {
  // return
  // 如果是编辑模式，不显示右键菜单
  if (isEditMode.value) {
    return
  }

  // 检查是否点击在任何弹窗内
  const isClickInModal =
    event.target.closest('.ant-modal') ||
    event.target.closest('.ant-modal-root') ||
    event.target.closest('.ant-modal-wrap') ||
    event.target.closest('.folder-modal') ||
    event.target.closest('.collection-modal') ||
    event.target.closest('.app-modal') ||
    event.target.closest('.icon-modal') ||
    event.target.closest('.ant-modal-content') ||
    // 检查是否有可见的模态窗口蒙层（而不是仅仅存在于DOM中）
    (document.querySelector('.ant-modal-mask') &&
      getComputedStyle(document.querySelector('.ant-modal-mask')).display !== 'none') ||
    appModalVisible.value || // 检查应用弹窗是否打开
    wallModalVisible.value ||
    folderModalVisible.value || // 检查文件夹弹窗是否打开
    collectionModalVisible.value || // 检查应用合集弹窗是否打开
    addIconModalVisible.value || // 检查添加图标弹窗是否打开
    editIconModalVisible.value; // 检查编辑图标弹窗是否打开

  // 如果点击在弹窗内，不显示右键菜单
  if (isClickInModal) {
    return;
  }
  isContextMenuActive.value = true

  // 记录当前操作的应用，如果没有传入app则为null（空白区域点击）
  activeApp.value = app || null
  // 如果有应用对象，记录是否是Dock栏中的项目
  if (app) {
    const isInDock = isDockItem || dockApps.value.some(dockApp => dockApp.id === app.id)
    activeApp.value.isInDock = isInDock
    activeApp.value.isDockItem = isDockItem
  }
  // 设置菜单位置
  const x = event.clientX
  const y = event.clientY
  // 菜单宽度
  const menuWidth = 200
  // 动态高度：根据菜单内容类型判断
  let menuHeight = 300
  if (!app) {
    menuHeight = 120
  } else if (activeApp.value && activeApp.value.isInDock) {
    menuHeight = 50 // Dock菜单只有一个"从收藏栏移除"选项，高度很小
  }
  // 计算下方剩余空间
  const spaceBelow = window.innerHeight - y
  let top = y

  // 如果是dock栏项目，总是向上弹出菜单
  if (activeApp.value && activeApp.value.isInDock) {
    top = y - menuHeight - 10 // 向上弹出，留10px间距
    // 确保不超出屏幕顶部
    if (top < 10) {
      top = 10
    }
  } else {
    // 非dock栏项目的原有逻辑
    // 如果下方空间不足，菜单往上弹出
    if (spaceBelow < menuHeight && y > menuHeight) {
      top = y - menuHeight
    } else if (spaceBelow < menuHeight) {
      // 如果上方空间也不够，贴底显示
      top = window.innerHeight - menuHeight - 10
      if (top < 0) top = 10 // 防止超出顶部
    }
  }
  // 横向同理，防止超出右侧
  let left = x
  if (x + menuWidth > window.innerWidth - 10) {
    left = window.innerWidth - menuWidth - 10
    if (left < 0) left = 10
  }
  menuPosition.value = {
    left: `${left}px`,
    top: `${top}px`
  }
  // 显示菜单
  showMenu.value = true
  // initSortable()
  // 使用nextTick确保在DOM更新后重置标志位
  nextTick(() => {
    // 延迟重置标志位，确保DOM完全更新
    setTimeout(() => {
      isContextMenuActive.value = false
    }, 100)
  })
}

// 设置应用尺寸
function setAppSize(width, height) {
  // 如果参数是一个对象（从ContextMenu组件传入），则解构出width和height
  if (typeof width === 'object' && width !== null) {
    height = width.height;
    width = width.width;
  }


  if (!activeApp.value) return

  // 如果尺寸没有变化，直接返回
  if (activeApp.value.size?.w === width && activeApp.value.size?.h === height) {
    showMenu.value = false
    return
  }

  // 设置标志位，标记正在修改应用尺寸，阻止网格监听器响应
  isChangingAppSize.value = true

  // FLIP动画实现 (First, Last, Invert, Play)
  const appElement = document.querySelector(`[data-id="${activeApp.value.id}"]`)
  if (appElement) {
    // First: 记录元素的初始状态
    const first = appElement.getBoundingClientRect()

    // 添加尺寸变化类，触发动画效果
    appElement.classList.add('size-changing');

    // 更新应用的尺寸
    activeApp.value.size = { w: width, h: height }

    // 更新所有应用中该应用的尺寸
    const appIndex = allApps.value.findIndex(app => app.id === activeApp.value.id)
    if (appIndex !== -1) {
      allApps.value[appIndex].size = { w: width, h: height }
      // 记录新的尺寸到数据模型中
      allApps.value[appIndex].w = width.toString()
      allApps.value[appIndex].h = height.toString()
    }

    // 查找应用在当前分类列表中的位置并更新
    const categoryAppsIndex = categoryApps.value[activeCategory.value].findIndex(app => app.id === activeApp.value.id)
    if (categoryAppsIndex !== -1) {
      // 更新大小
      categoryApps.value[activeCategory.value][categoryAppsIndex].size = { w: width, h: height }
      // 记录新的尺寸到数据模型中
      categoryApps.value[activeCategory.value][categoryAppsIndex].w = width.toString()
      categoryApps.value[activeCategory.value][categoryAppsIndex].h = height.toString()

    }

    // 同步到 navigation store
    syncToNavigationStore(activeCategory.value, categoryApps.value[activeCategory.value])

    // 保存更新后的应用设置
    saveAppOrder()

    // 关闭菜单
    showMenu.value = false

    // 下一个渲染周期执行剩余的FLIP动画步骤
    nextTick(() => {
      // Last: 获取元素最终状态
      const last = appElement.getBoundingClientRect()

      // Invert: 计算差异并应用反向变换
      const deltaX = first.width / last.width
      const deltaY = first.height / last.height

      // 对所有卡片应用动画，让它们平滑移动到新位置
      const allCards = document.querySelectorAll('.app-item');
      allCards.forEach(card => {
        if (card !== appElement) {
          card.style.transition = 'transform 0.8s cubic-bezier(0.34, 1.56, 0.64, 1)';
        }
      });

      // 应用初始缩放变换，让元素看起来还在原来的位置和大小
      appElement.style.transformOrigin = 'top left'
      appElement.style.transform = `scale(${deltaX}, ${deltaY})`

      // Play: 播放动画到最终状态
      requestAnimationFrame(() => {
        appElement.style.transition = 'transform 0.7s cubic-bezier(0.34, 1.56, 0.64, 1)'
        appElement.style.transform = 'scale(1, 1)'

        // 动画结束后清理样式
        appElement.addEventListener('transitionend', () => {
          appElement.style.transition = ''
          appElement.style.transform = ''
          appElement.style.transformOrigin = ''
          appElement.classList.remove('size-changing');

          // 清理其他卡片的过渡效果
          allCards.forEach(card => {
            if (card !== appElement) {
              card.style.transition = '';
            }
          });

          // 重新计算和保存所有卡片的位置
          updateAllAppPositions();

          // 重置尺寸修改标志位
          isChangingAppSize.value = false
        }, { once: true })
      })
    })
  } else {
    // 如果找不到DOM元素，仍然更新数据
    activeApp.value.size = { w: width, h: height }

    const appIndex = allApps.value.findIndex(app => app.id === activeApp.value.id)
    if (appIndex !== -1) {
      allApps.value[appIndex].size = { w: width, h: height }
      // 记录新的尺寸到数据模型中
      allApps.value[appIndex].w = width.toString()
      allApps.value[appIndex].h = height.toString()
    }

    // 查找应用在当前分类列表中的位置并更新
    const categoryAppsIndex = categoryApps.value[activeCategory.value].findIndex(app => app.id === activeApp.value.id)
    if (categoryAppsIndex !== -1) {
      // 更新大小
      categoryApps.value[activeCategory.value][categoryAppsIndex].size = { w: width, h: height }
      // 记录新的尺寸到数据模型中
      categoryApps.value[activeCategory.value][categoryAppsIndex].w = width.toString()
      categoryApps.value[activeCategory.value][categoryAppsIndex].h = height.toString()
    }

    saveAppOrder()
    showMenu.value = false

    nextTick(() => {
      // 重新计算和保存所有卡片的位置
      updateAllAppPositions();

      // 重置尺寸修改标志位
      isChangingAppSize.value = false

      initSortable()
    })

    // 触发自动备份
    autoBackupManager.triggerAutoBackup()
  }
}

// 点击区域外关闭菜单
function handleClickOutside(event) {
  // 处理右键菜单关闭 - 现在由ContextMenu组件处理

  // 处理编辑模式关闭 - 简化逻辑，只要不是点击删除按钮就退出编辑模式
  if (isEditMode.value) {
    const deleteButtons = document.querySelectorAll('.delete-icon');
    const clickedOnDelete = Array.from(deleteButtons).some(btn => btn.contains(event.target));
    const isContextMenuEvent = event.target.closest('.context-menu');

    // 检查是否点击在删除确认对话框内
    const isDeleteConfirmModal = event.target.closest('.ant-modal') &&
      (event.target.closest('.ant-modal-content')?.textContent?.includes('确认删除') ||
        event.target.closest('.ant-modal-content')?.textContent?.includes('确定要删除'));

    // 如果没有点击删除按钮、右键菜单或删除确认对话框，退出编辑模式
    if (!clickedOnDelete && !isContextMenuEvent && !isDeleteConfirmModal) {
      isEditMode.value = false;

      // 退出编辑模式后重新初始化排序
      nextTick(() => {
        initSortable();
      });
    }
  }
}

// 隐藏右键菜单
function hideContextMenu() {
  // 🔍 右键菜单调试 - 隐藏菜单时的详细信息
  isContextMenuActive.value = true;

  // 隐藏菜单
  showMenu.value = false;

  // 延迟重置标志位
  setTimeout(() => {
    isContextMenuActive.value = false;
    initSortable()
  }, 100);
}

// 重置分类应用为默认顺序
function resetCategoryApps() {
  const categories = ['home', 'documents', 'gallery', 'favorites', 'settings']
  categories.forEach(category => {
    categoryApps.value[category] = allApps.value.filter(app => app.category === category)
  })
  saveAppOrder()
}

// 添加移动应用到分类的功能
function moveAppToCategory(category) {
  if (!activeApp.value) return;

  // 检查应用是否已在目标分类中
  if (activeApp.value.category === category) {
    showMenu.value = false;
    return;
  }

  // 从当前分类中移除应用
  const currentCategory = activeApp.value.category;
  const currentCategoryIndex = categoryApps.value[currentCategory].findIndex(app => app.id === activeApp.value.id);

  if (currentCategoryIndex !== -1) {
    categoryApps.value[currentCategory].splice(currentCategoryIndex, 1);
    // 同步到 navigation store
    syncToNavigationStore(currentCategory, categoryApps.value[currentCategory]);
  }

  // 更新应用的分类
  const appIndex = allApps.value.findIndex(app => app.id === activeApp.value.id);
  if (appIndex !== -1) {
    allApps.value[appIndex].category = category;
    activeApp.value.category = category;
  }

  // 将应用添加到新分类中
  categoryApps.value[category].push(activeApp.value);

  // 同步到 navigation store
  syncToNavigationStore(category, categoryApps.value[category]);

  // 保存更新后的数据
  saveAppOrder();

  // 关闭菜单
  showMenu.value = false;

  // 触发自动备份
  autoBackupManager.triggerAutoBackup();

  // 注意：displayedApps 现在是计算属性，会自动更新
}

// 设置打开方式
function setOpenMode(mode) {
  if (!activeApp.value) return;

  // 更新应用的打开方式
  activeApp.value.iscanopen = mode;

  // 更新allApps中对应的应用
  const appIndex = allApps.value.findIndex(app => app.id === activeApp.value.id);
  if (appIndex !== -1) {
    allApps.value[appIndex].iscanopen = mode;
  }

  // 更新categoryApps中对应的应用
  const categoryAppsIndex = categoryApps.value[activeCategory.value]?.findIndex(app => app.id === activeApp.value.id);
  if (categoryAppsIndex !== -1) {
    categoryApps.value[activeCategory.value][categoryAppsIndex].iscanopen = mode;
  }

  // 更新 dockApps 中对应的应用
  const dockAppIndex = dockApps.value.findIndex(app => app.id === activeApp.value.id);
  if (dockAppIndex !== -1) {
    dockApps.value[dockAppIndex].iscanopen = mode;
    // 保存到localStorage
    const storageKey = getCurrentCollectStorageKey()
    localStorage.setItem(storageKey, JSON.stringify(dockApps.value))
  }
  
  // 同步到 navigation store
  if (categoryApps.value[activeCategory.value]) {
    syncToNavigationStore(activeCategory.value, categoryApps.value[activeCategory.value]);
  }

  // 保存更新后的应用设置
  saveAppOrder();

  // 触发自动备份
  autoBackupManager.triggerAutoBackup();

  // 关闭菜单
  showMenu.value = false;
}

// 删除应用功能
function deleteApp() {
  if (!activeApp.value) return;

  Modal.confirm({
    title: '确认删除',
    content: `确定要删除 ${activeApp.value.name} 吗？`,
    okText: '确认',
    cancelText: '取消',
    centered: true, // 让对话框垂直居中显示
    onOk() {
      const currentApps = activeApp.value
      const token = localStorage.getItem('token')
      if (token && currentApps.type == 'app') {
        deleteUserApp(currentApps.id).then((res) => {
          if (res.status == 200) {
            console.log('删除成功')
          }
        })
      } else if (token && currentApps.type == 'folder') {
        deleteFolder(currentApps.id).then((res) => {
          if (res.status == 200) {
            console.log('删除成功')
          }
        })
      }
      // 从allApps中删除
      const appIndex = allApps.value.findIndex(app => app.id === activeApp.value.id);
      if (appIndex !== -1) {
        allApps.value.splice(appIndex, 1);
      }

      // 从categoryApps中删除
      const category = activeApp.value.category;
      const categoryAppIndex = categoryApps.value[category].findIndex(app => app.id === activeApp.value.id);
      if (categoryAppIndex !== -1) {
        categoryApps.value[category].splice(categoryAppIndex, 1);
        // 同步到 navigation store
        syncToNavigationStore(category, categoryApps.value[category]);
      }

      // 保存更新（displayedApps 现在是计算属性，会自动更新）
      saveAppOrder();

      // 等待DOM更新后重新初始化Sortable
      nextTick(() => {
        initSortable();
      });
    }
  });
}

// 纯净模式状态
const isPureMode = ref(false)
// 记住进入纯净模式前的数据源
const lastDataSourceBeforePure = ref('')

// 数据源切换相关状态 - 现在完全依赖 Navigation Store
const isLoadingDataSource = ref(false)
// 动画状态
const isPageFlipping = ref(false)

// 使用CSS keyframes的fade动画函数
function createFadeAnimation(container, onDataSwitch) {
  return new Promise((resolve, reject) => {
    try {
      // 阶段1：添加淡出动画类
      container.classList.add('fade-out')

      // 阶段2：数据切换点 (0.6s后，淡出动画完成)
      setTimeout(async () => {
        try {
          // 移除淡出类
          container.classList.remove('fade-out')

          if (onDataSwitch) {
            await onDataSwitch()
          }

          // 阶段3：添加淡入动画类
          container.classList.add('fade-in')

          // 动画完成后清理样式 (1.2s后，淡入动画完成)
          setTimeout(() => {
            container.classList.remove('fade-in')
            resolve()
          }, 500)

        } catch (error) {
          reject(error)
        }
      }, 500)

    } catch (error) {
      reject(error)
    }
  })
}

// 创建简单的加载指示器
function createSimpleLoadingIndicator() {
  const loadingEl = document.createElement('div')
  loadingEl.className = 'simple-loading-indicator'
  loadingEl.innerHTML = `
    <div class="loading-card">
      <div class="loading-text">正在切换数据源...</div>
    </div>
  `
  document.body.appendChild(loadingEl)

  return { element: loadingEl }
}


// 数据源切换函数
async function switchDataSource(message) {
  // 防止重复调用和动画期间调用
  if (isLoadingDataSource.value || isPageFlipping.value) return

  // 定义数据源切换顺序，根据message参数控制切换逻辑
  let nextDataSource

  if (message === 'noPure') {
    // 只在娱乐和办公模式之间切换，不包含纯净模式
    nextDataSource = navigationStore.currentDataSource === 'entertainment' ? 'office' : 'entertainment'
  } else if (message === 'noEntertain') {
    // 只在办公和纯净模式之间切换，不包含娱乐模式
    if (isPureMode.value) {
      nextDataSource = 'office'
    } else if (navigationStore.currentDataSource === 'office') {
      nextDataSource = 'pure'
    } else {
      // 如果当前是娱乐模式，先切换到办公模式
      nextDataSource = 'office'
    }
  } else {
    // 默认三元循环切换（娱乐 → 办公 → 纯净 → 娱乐）
    if (isPureMode.value) {
      // 从纯净模式切换出来，根据记住的数据源决定下一个数据源
      if (lastDataSourceBeforePure.value === 'entertainment') {
        nextDataSource = 'office'
      } else {
        nextDataSource = 'entertainment'
      }
    } else if (navigationStore.currentDataSource === 'entertainment') {
      // 从娱乐模式切换到办公模式
      nextDataSource = 'office'
    } else {
      // 从办公模式切换到纯净模式
      nextDataSource = 'pure'
    }
  }

  // 数据源名称映射
  const dataSourceNames = {
    entertainment: '娱乐模式',
    office: '办公模式',
    pure: '纯净模式'
  }

  // 处理纯净模式切换
  if (nextDataSource === 'pure') {
    // 记住进入纯净模式前的数据源
    lastDataSourceBeforePure.value = navigationStore.currentDataSource

    // 切换到纯净模式
    isPureMode.value = true
    localStorage.setItem('isPureMode', true)

    // 添加body全局类，用于控制侧边栏显示
    document.body.classList.add('pure-mode-active')

    // 向HeaderBar发送纯净模式状态变更事件
    window.dispatchEvent(new CustomEvent('pure-mode-changed', {
      detail: { isPureMode: true }
    }))

    // 发送模式变化事件给轮盘
    emitter.emit('mode-changed')

    // 显示切换提示
    // message.success({
    //   content: `已切换到${dataSourceNames[nextDataSource]}`,
    //   duration: 2
    // })

    return
  }

  // 检查是否需要使用动画
  // noEntertain模式下，纯净模式与办公模式之间的切换不使用动画
  // 临时注释：统一不使用动画进行测试
  // const shouldUseAnimation = !(message === 'noEntertain' &&
  //   ((isPureMode.value && nextDataSource === 'office') ||
  //    (navigationStore.currentDataSource === 'office' && nextDataSource === 'pure')))
  const shouldUseAnimation = false // 临时禁用所有动画

  // 如果从纯净模式切换出来，重置纯净模式状态
  if (isPureMode.value) {
    isPureMode.value = false
    localStorage.setItem('isPureMode', false)
    document.body.classList.remove('pure-mode-active')

    // 向HeaderBar发送纯净模式状态变更事件
    window.dispatchEvent(new CustomEvent('pure-mode-changed', {
      detail: { isPureMode: false }
    }))

    // 如果不需要动画，直接进行数据切换
    if (!shouldUseAnimation) {
      try {
        // 直接进行数据切换，不使用动画
        await navigationStore.setDataSource(nextDataSource)

        // 根据数据源设置默认壁纸（仅在用户未自定义时）
        // wallpaperStore.setDefaultWallpaperByDataSource(nextDataSource)

        // 更新全局导航数据（从 Navigation Store 获取）
        allNavItems.value = navigationStore.allNavItems

        // 设置当前分类（从 Navigation Store 获取）
        if (navigationStore.allNavItems.length > 0) {
          const firstCategory = navigationStore.allNavItems[0]
          currentMenuType.value = firstCategory.type

          // 更新分类数据为当前数据源的分类
          menuCategories.value = navigationStore.allNavItems.map(item => ({
            type: item.type,
            icon: item.ico
          }));
        }

        // 重新初始化dock栏数据（因为数据源已切换）
        initDockApps()

        // 重新初始化UI
        nextTick(() => {
          initSortable()
          updateDisplayedApps()
          // 重新初始化滑动切换功能，确保使用新的分类数据
          initSwipeNavigation()
        })

        // 发送模式变化事件给轮盘
        emitter.emit('mode-changed')

        return // 直接返回，不执行后续的动画

      } catch (error) {
        console.error('数据切换失败:', error)
        message.error({
          content: `切换失败: ${error.message}`,
          duration: 3
        })
        return
      }
    }

    // 临时注释：统一不使用动画，直接执行切换逻辑
    // 如果需要动画且是从纯净模式切换到办公模式，使用统一的渐显渐隐动画
    // if (shouldUseAnimation && nextDataSource === 'office') {
    //   // 开始简单的渐显渐隐动画
    //   isPageFlipping.value = true
    //   isLoadingDataSource.value = true

    //   const homeContainer = document.querySelector('.home-container')
    //   if (!homeContainer) {
    //     console.error('找不到home-container元素')
    //     return
    //   }

    //   // 创建简单加载指示器
    //   const loadingIndicator = createSimpleLoadingIndicator()

    //   // 数据切换回调函数
    //   const handleDataSwitch = async () => {
    //     try {
    //       // 直接进行数据切换
    //       await navigationStore.setDataSource(nextDataSource)

    //       // 根据数据源设置默认壁纸（仅在用户未自定义时）
    //       // wallpaperStore.setDefaultWallpaperByDataSource(nextDataSource)

    //       // 更新全局导航数据（从 Navigation Store 获取）
    //       allNavItems.value = navigationStore.allNavItems

    //       // 设置当前分类（从 Navigation Store 获取）
    //       if (navigationStore.allNavItems.length > 0) {
    //         const firstCategory = navigationStore.allNavItems[0]
    //         currentMenuType.value = firstCategory.type

    //         // 更新分类数据为当前数据源的分类
    //         menuCategories.value = navigationStore.allNavItems.map(item => ({
    //           type: item.type,
    //           icon: item.ico
    //         }));
    //       }

    //       // 重新初始化dock栏数据（因为数据源已切换）
    //       initDockApps()

    //       // 重新初始化UI
    //       nextTick(() => {
    //         initSortable()
    //         updateDisplayedApps()
    //       })

    //     } catch (error) {
    //       console.error('数据切换失败:', error)
    //       throw error
    //     }
    //   }

    //   try {
    //     // 启动fade动画
    //     await createFadeAnimation(homeContainer, handleDataSwitch)

    //   } catch (error) {
    //     console.error('切换数据源失败:', error)
    //     message.error({
    //       content: `切换失败: ${error.message}`,
    //       duration: 3
    //     })

    //     // 错误时重置容器状态
    //     homeContainer.classList.remove('fade-out', 'fade-in')
    //     homeContainer.style.opacity = '1'

    //   } finally {
    //     // 清理加载指示器
    //     if (loadingIndicator && loadingIndicator.element) {
    //       document.body.removeChild(loadingIndicator.element)
    //     }

    //     // 重置状态
    //     isPageFlipping.value = false
    //     isLoadingDataSource.value = false
    //   }

    //   return // 直接返回，不执行后续的动画
    // }
  }

  // 如果不需要动画，直接进行数据切换
  if (!shouldUseAnimation) {
    try {
      // 直接进行数据切换，不使用动画
      await navigationStore.setDataSource(nextDataSource)

      // 根据数据源设置默认壁纸（仅在用户未自定义时）
      // wallpaperStore.setDefaultWallpaperByDataSource(nextDataSource)

      // 更新全局导航数据（从 Navigation Store 获取）
      allNavItems.value = navigationStore.allNavItems

      // 设置当前分类（从 Navigation Store 获取）
      if (navigationStore.allNavItems.length > 0) {
        const firstCategory = navigationStore.allNavItems[0]
        currentMenuType.value = firstCategory.type

        // 更新分类数据为当前数据源的分类
        menuCategories.value = navigationStore.allNavItems.map(item => ({
          type: item.type,
          icon: item.ico
        }));
      }

      // 重新初始化dock栏数据（因为数据源已切换）
      initDockApps()

      // 重新初始化UI
      nextTick(() => {
        initSortable()
        updateDisplayedApps()
      })

      return // 直接返回，不执行动画

    } catch (error) {
      console.error('数据切换失败:', error)
      message.error({
        content: `切换失败: ${error.message}`,
        duration: 3
      })
      return
    }
  }

  // 临时注释：统一不使用动画，直接执行切换逻辑
  // 开始简单的渐显渐隐动画（仅在需要动画时执行）
  // isPageFlipping.value = true
  // isLoadingDataSource.value = true

  // const homeContainer = document.querySelector('.home-container')
  // if (!homeContainer) {
  //   console.error('找不到home-container元素')
  //   return
  // }

  // // 创建简单加载指示器
  // const loadingIndicator = createSimpleLoadingIndicator()

  // // 数据切换回调函数
  // const handleDataSwitch = async () => {
  //   try {
  //     // 完全委托给 Navigation Store 处理数据源切换
  //     await navigationStore.setDataSource(nextDataSource)

  //     // 根据数据源设置默认壁纸（仅在用户未自定义时）
  //     // wallpaperStore.setDefaultWallpaperByDataSource(nextDataSource)

  //     // 更新全局导航数据（从 Navigation Store 获取）
  //     allNavItems.value = navigationStore.allNavItems

  //     // 设置当前分类（从 Navigation Store 获取）
  //     if (navigationStore.allNavItems.length > 0) {
  //       const firstCategory = navigationStore.allNavItems[0]
  //       currentMenuType.value = firstCategory.type

  //       // 更新分类数据为当前数据源的分类
  //       menuCategories.value = navigationStore.allNavItems.map(item => ({
  //         type: item.type,
  //         icon: item.ico
  //       }));
  //     }

  //     // 重新初始化dock栏数据（因为数据源已切换）
  //     initDockApps()

  //     // 重新初始化UI
  //     nextTick(() => {
  //       initSortable()
  //       updateDisplayedApps()
  //     })

  //   } catch (error) {
  //     console.error('数据切换失败:', error)
  //     throw error
  //   }
  // }

  // try {
  //   // 启动fade动画
  //   await createFadeAnimation(homeContainer, handleDataSwitch)

  //   // 显示成功提示
  //   // message.success({
  //   //   content: `已切换到${dataSourceNames[nextDataSource]}`,
  //   //   duration: 2
  //   // })

  // } catch (error) {
  //   console.error('切换数据源失败:', error)
  //   message.error({
  //     content: `切换失败: ${error.message}`,
  //     duration: 3
  //   })

  //   // 错误时重置容器状态
  //   homeContainer.classList.remove('fade-out', 'fade-in')
  //   homeContainer.style.opacity = '1'

  // } finally {
  //   // 清理加载指示器
  //   if (loadingIndicator && loadingIndicator.element) {
  //     document.body.removeChild(loadingIndicator.element)
  //   }

  //   // 重置状态
  //   isPageFlipping.value = false
  //   isLoadingDataSource.value = false
  // }

  // 临时替代：直接执行数据切换逻辑，不使用动画
  try {
    // 完全委托给 Navigation Store 处理数据源切换
    await navigationStore.setDataSource(nextDataSource)

    // 更新全局导航数据（从 Navigation Store 获取）
    allNavItems.value = navigationStore.allNavItems

    // 设置当前分类（从 Navigation Store 获取）
    if (navigationStore.allNavItems.length > 0) {
      const firstCategory = navigationStore.allNavItems[0]
      currentMenuType.value = firstCategory.type

      // 更新分类数据为当前数据源的分类
      menuCategories.value = navigationStore.allNavItems.map(item => ({
        type: item.type,
        icon: item.ico
      }));
    }

    // 重新初始化dock栏数据（因为数据源已切换）
    initDockApps()

    // 重新初始化UI
    nextTick(() => {
      initSortable()
      updateDisplayedApps()
    })

  } catch (error) {
    console.error('数据切换失败:', error)
    message.error({
      content: `切换失败: ${error.message}`,
      duration: 3
    })
  }
}

// 处理双击事件
function handleDoubleClick(event) {
  // 阻止默认行为，防止选中文字
  event.preventDefault()

  // 临时注释：不检查动画状态
  // 如果正在翻页动画中，不响应双击
  // if (isPageFlipping.value) {
  //   return
  // }

  // 检查是否点击在非空白区域元素上或任何弹窗内
  const isClickOnInteractiveElement =
    event.target.closest('.app-item') ||
    event.target.closest('.menu-item') ||
    event.target.closest('.delete-icon') ||
    event.target.closest('.search') ||
    event.target.closest('.search-pure') ||
    event.target.closest('.context-menu')

  // 检查是否点击在任何弹窗内
  const isClickInModal =
    event.target.closest('.ant-modal') ||
    event.target.closest('.ant-modal-root') ||
    event.target.closest('.ant-modal-wrap') ||
    event.target.closest('.folder-modal') ||
    event.target.closest('.collection-modal') ||
    event.target.closest('.app-modal') ||
    event.target.closest('.icon-modal') ||
    event.target.closest('.ant-modal-content') ||
    // 检查是否有可见的模态窗口蒙层（而不是仅仅存在于DOM中）
    (document.querySelector('.ant-modal-mask') &&
      getComputedStyle(document.querySelector('.ant-modal-mask')).display !== 'none') ||
    appModalVisible.value ||
    wallModalVisible.value ||
    folderModalVisible.value ||
    collectionModalVisible.value ||
    addIconModalVisible.value ||
    editIconModalVisible.value

  // 如果点击在交互元素上或任何弹窗内，不触发数据源切换
  if (!isClickOnInteractiveElement && !isClickInModal) {
    // 调用数据源切换函数 - 修复逻辑，确保正确的三元循环切换
    // 娱乐模式(entertainment) -> 办公模式(office) -> 纯净模式(pure) -> 娱乐模式...
    
    if (isPureMode.value) {
      // 如果当前是纯净模式，切换到娱乐模式
      switchDataSource()
    } else if (navigationStore.currentDataSource === 'entertainment') {
      // 如果当前是娱乐模式，切换到办公模式
      switchDataSource('noPure')
    } else if (navigationStore.currentDataSource === 'office') {
      // 如果当前是办公模式，切换到纯净模式
      switchDataSource()
    } else {
      // 默认情况，进行常规切换
      switchDataSource()
    }
  }
}

// 直接切换到指定模式（轮盘专用）
async function switchToTargetMode(targetMode) {
  // 防止重复调用和动画期间调用
  if (isLoadingDataSource.value || isPageFlipping.value) return

  console.log(`🎯 轮盘切换到模式: ${targetMode}`)

  try {
    if (targetMode === 'pure') {
      // 切换到纯净模式
      if (!isPureMode.value) {
        // 记住进入纯净模式前的数据源
        lastDataSourceBeforePure.value = navigationStore.currentDataSource

        // 切换到纯净模式
        isPureMode.value = true
        localStorage.setItem('isPureMode', true)

        // 添加body全局类，用于控制侧边栏显示
        document.body.classList.add('pure-mode-active')

        // 向HeaderBar发送纯净模式状态变更事件
        window.dispatchEvent(new CustomEvent('pure-mode-changed', {
          detail: { isPureMode: true }
        }))

        // 发送模式变化事件给轮盘
        emitter.emit('mode-changed')
      }
    } else {
      // 切换到娱乐或办公模式
      // 如果当前是纯净模式，先退出纯净模式
      if (isPureMode.value) {
        isPureMode.value = false
        localStorage.setItem('isPureMode', false)
        document.body.classList.remove('pure-mode-active')

        // 向HeaderBar发送纯净模式状态变更事件
        window.dispatchEvent(new CustomEvent('pure-mode-changed', {
          detail: { isPureMode: false }
        }))
      }

      // 切换数据源
      if (navigationStore.currentDataSource !== targetMode) {
        await navigationStore.setDataSource(targetMode)

        // 更新全局导航数据
        allNavItems.value = navigationStore.allNavItems

        // 设置当前分类
        if (navigationStore.allNavItems.length > 0) {
          const firstCategory = navigationStore.allNavItems[0]
          currentMenuType.value = firstCategory.type

          // 更新分类数据
          menuCategories.value = navigationStore.allNavItems.map(item => ({
            type: item.type,
            icon: item.ico
          }))
        }

        // 发送模式变化事件给轮盘
        emitter.emit('mode-changed')
      }
    }
  } catch (error) {
    console.error('切换模式失败:', error)
  }
}

// 切换纯净模式
function togglePureMode(event) {
  // 如果是从右键菜单调用（没有event参数）直接切换模式
  if (!event) {
    // 直接切换纯净模式
    isPureMode.value = !isPureMode.value

    // 保存到本地存储以便下次访问时记住用户的偏好
    localStorage.setItem('isPureMode', isPureMode.value)

    // 如果进入纯净模式，关闭右键菜单
    if (isPureMode.value && showMenu.value) {
      showMenu.value = false
    }

    // 添加/移除body全局类，用于控制侧边栏显示
    if (isPureMode.value) {
      document.body.classList.add('pure-mode-active')
    } else {
      document.body.classList.remove('pure-mode-active')
    }

    // 向HeaderBar发送纯净模式状态变更事件
    window.dispatchEvent(new CustomEvent('pure-mode-changed', {
      detail: { isPureMode: isPureMode.value }
    }))

    return
  }

  // 阻止默认行为，防止选中文字
  event.preventDefault()

  // 检查是否点击在非空白区域元素上或任何弹窗内
  const isClickOnInteractiveElement =
    event.target.closest('.app-item') ||
    event.target.closest('.menu-item') ||
    event.target.closest('.delete-icon') ||
    event.target.closest('.search') ||
    event.target.closest('.search-pure') ||
    event.target.closest('.context-menu');

  // 检查是否点击在任何弹窗内
  const isClickInModal =
    event.target.closest('.ant-modal') ||
    event.target.closest('.ant-modal-root') ||
    event.target.closest('.ant-modal-wrap') ||
    event.target.closest('.folder-modal') ||
    event.target.closest('.collection-modal') ||
    event.target.closest('.app-modal') ||
    event.target.closest('.icon-modal') ||
    event.target.closest('.ant-modal-content') ||
    // 检查是否有可见的模态窗口蒙层（而不是仅仅存在于DOM中）
    (document.querySelector('.ant-modal-mask') &&
      getComputedStyle(document.querySelector('.ant-modal-mask')).display !== 'none') ||
    appModalVisible.value || // 检查应用弹窗是否打开
    wallModalVisible.value ||
    folderModalVisible.value || // 检查文件夹弹窗是否打开
    collectionModalVisible.value || // 检查应用合集弹窗是否打开
    addIconModalVisible.value || // 检查添加图标弹窗是否打开
    editIconModalVisible.value; // 检查编辑图标弹窗是否打开

  // 如果点击在交互元素上或任何弹窗内，不触发纯净模式
  if (!isClickOnInteractiveElement && !isClickInModal) {
    // 向HeaderBar发送纯净模式状态变更事件
    window.dispatchEvent(new CustomEvent('pure-mode-changed', {
      detail: { isPureMode: isPureMode.value }
    }))
  }
}

// 编辑模式状态
const isEditMode = ref(false)

// 切换编辑模式
function toggleEditMode() {
  isEditMode.value = !isEditMode.value
  showMenu.value = false // 关闭菜单

  // 退出编辑模式时，重新初始化排序
  if (!isEditMode.value) {
    nextTick(() => {
      initSortable()
    })
  }
  // 移除了进入编辑模式时禁用拖动排序的代码
}

emitter.on('open-model-url', (event) => {
  openApp(event)
})



// 进入纯净模式时退出编辑模式
watch(isPureMode, (newValue) => {
  if (newValue && isEditMode.value) {
    isEditMode.value = false
  }
})


// 组件属性
const props = defineProps({
  isCollapsed: {
    type: Boolean,
    default: false
  }
})

// 组件事件
const emit = defineEmits(['toggle'])

// 导航栏折叠状态
const isCollapsedLocal = ref(props.isCollapsed)


// dock栏应用
const dockApps = ref([])

// 根据当前数据源获取收藏API
function getCurrentCollectAPI() {
  return navigationStore.currentDataSource === 'office' ? getOfficeCollectList : getCollectList
}

// 根据当前数据源获取收藏数据的localStorage键
function getCurrentCollectStorageKey() {
  return navigationStore.currentDataSource === 'office' ? 'officeDockApps' : 'homeDockApps'
}

emitter.on('initdock', () => {
  initDockApps()
})


// 初始化dock栏数据
function initDockApps() {
  const storageKey = getCurrentCollectStorageKey()
  const savedDockApps = localStorage.getItem(storageKey)
  if (savedDockApps) {
    dockApps.value = JSON.parse(savedDockApps)
  } else {
    // 如果本地没有，根据数据源用对应API初始化
    const collectAPI = getCurrentCollectAPI()
    collectAPI().then(res => {
      if (res.status === 200 && Array.isArray(res.data)) {
        dockApps.value = res.data.map(item => ({
          id: item.id,
          name: item.name,
          icon: item.logo,
          color: item.color,
          description: item.descs,
          url: item.websiteAddress,
          websiteAddress: item.websiteAddress,
          iscanopen: item.iscanopen,
          isfixed: item.isfixed  // 0取消固定 1固定
        }))
        // 首次拉取后也要持久化
        localStorage.setItem(storageKey, JSON.stringify(dockApps.value))
      } else {
        console.warn(`API返回数据异常:`, res)
      }
    }).catch(error => {
      console.error(`加载${navigationStore.currentDataSource}模式收藏数据失败:`, error)
    })
  }
}

// 添加到收藏栏（dockApps），深拷贝，避免与主应用列表共用引用
function addToDock(app) {
  // 先去重
  let dockList = dockApps.value.filter(item => item.id !== app.id)
  // 添加新应用到末尾
  dockList.push(JSON.parse(JSON.stringify({
    id: app.id,
    name: app.name,
    icon: app.icon || app.logo,
    color: app.color,
    description: app.description,
    url: app.url,
    websiteAddress: app.websiteAddress,
    iscanopen: app.iscanopen,
    type: app.type,
    isfixed: app.isfixed || 0
  })))
  // DockBar组件会自动显示末尾12个应用，固定的应用优先显示在前面
  dockApps.value = dockList

  // 根据当前数据源选择localStorage键
  const storageKey = getCurrentCollectStorageKey()
  localStorage.setItem(storageKey, JSON.stringify(dockList))

  // 刷新 navigation store 的收藏数据
  navigationStore.refreshCollectAppsData()

  // 触发自动备份
  autoBackupManager.triggerAutoBackup()
}
// 从收藏栏移除
function removeFromDock(app) {
  let dockList = dockApps.value.filter(item => item.id !== app.id)
  dockApps.value = dockList

  // 根据当前数据源选择localStorage键
  const storageKey = getCurrentCollectStorageKey()
  localStorage.setItem(storageKey, JSON.stringify(dockList))

  // 刷新 navigation store 的收藏数据
  navigationStore.refreshCollectAppsData()

  // 触发自动备份
  autoBackupManager.triggerAutoBackup()
}

// 固定应用到收藏栏
function fixApp(app) {
  const token = localStorage.getItem('token')
  if (token) {
    // 调用API固定应用
    fixedApp(app.id).then((res) => {
      if (res.status == 200) {
        console.log('固定成功')
        // 更新本地数据中的固定状态
        const dockApp = dockApps.value.find(item => item.id === app.id)
        if (dockApp) {
          dockApp.isfixed = 1 // 1表示固定

          // 根据当前数据源选择localStorage键
          const storageKey = getCurrentCollectStorageKey()
          localStorage.setItem(storageKey, JSON.stringify(dockApps.value))

          // 刷新 navigation store 的收藏数据
          navigationStore.refreshCollectAppsData()
        }
      }
    }).catch((error) => {
      console.error('固定失败:', error)
    })
  }
}

// 取消固定应用
function unfixApp(app) {
  const token = localStorage.getItem('token')
  if (token) {
    // 调用API取消固定应用
    cancalfixedApp(app.id).then((res) => {
      if (res.status == 200) {
        console.log('取消固定成功')
        // 更新本地数据中的固定状态
        const dockApp = dockApps.value.find(item => item.id === app.id)
        if (dockApp) {
          dockApp.isfixed = 0 // 0表示未固定

          // 根据当前数据源选择localStorage键
          const storageKey = getCurrentCollectStorageKey()
          localStorage.setItem(storageKey, JSON.stringify(dockApps.value))

          // 刷新 navigation store 的收藏数据
          navigationStore.refreshCollectAppsData()
        }
      }
    }).catch((error) => {
      console.error('取消固定失败:', error)
    })
  }
}

// 应用弹窗相关状态
const appModalVisible = ref(false)
const wallModalVisible = ref(false)
const activeAppUrl = ref('')
const activeAppTitle = ref('')

// 打开应用
function openApp(app) {
  // 如果是URL类应用，可以打开链接
  if (app.url) {
    // 构建URL（确保正确的协议）
    let url = app.url
    if (url.indexOf('?') == -1) {
      url = url + '?ref=https://linkfun.fun/'
    } else {
      url = url + '&ref=https://linkfun.fun/'
    }
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = `https://${app.url}`
    }

    if (app.iscanopen === 2) {
      window.open(url, '_blank')
    } else if (app.iscanopen === 3) {
      window.location.href = url
    } else {
      // 打开站内弹窗
      activeAppUrl.value = url
      activeAppTitle.value = app.name
      appModalVisible.value = true
    }
  }
}

// 关闭应用弹窗
function closeAppModal() {
  appModalVisible.value = false
  activeAppUrl.value = ''
}

function closeWallModal() {
  wallModalVisible.value = false
}

// 添加自定义图标的模态窗口
const addIconModalVisible = ref(false)
const iconModalEditMode = ref(false)
const editingIcon = ref(null)

// 编辑图标的模态窗口
const editIconModalVisible = ref(false)







// 显示添加自定义图标的模态窗口
function showAddIconModal() {
  iconModalEditMode.value = false
  editingIcon.value = null
  addIconModalVisible.value = true
}

// 显示编辑图标的模态窗口
function showEditIconModal(app) {
  editingIcon.value = { ...app }
  editIconModalVisible.value = true
}

// 处理保存编辑的图标
function handleSaveEditIcon(updatedIcon) {
  // 找到要更新的应用在当前分类应用列表中的索引
  const currentApps = getCurrentCategoryApps()
  const appIndex = currentApps.findIndex(app => app.id === updatedIcon.id)

  if (appIndex !== -1) {
    // 更新应用数据
    currentApps[appIndex] = { ...currentApps[appIndex], ...updatedIcon }

    // 更新当前分类的应用数据
    updateCurrentCategoryApps(currentApps)

    // 保存应用顺序到本地存储
    saveAppOrder()

    // 显示成功消息
    message.success('图标编辑成功')
  } else {
    message.error('未找到要编辑的图标')
  }
}



// 计算并保存所有应用的位置
function updateAllAppPositions() {
  if (activeCategory.value === 'all') return

  const appItems = document.querySelectorAll('.app-item')
  const appPositions = []

  // 收集所有应用的位置信息
  appItems.forEach(item => {
    const id = parseInt(item.getAttribute('data-id'))
    const rect = item.getBoundingClientRect()
    const gridRect = item.parentElement.getBoundingClientRect()

    // 计算相对于网格的位置
    const relativeX = rect.left - gridRect.left
    const relativeY = rect.top - gridRect.top

    // 估算网格单元大小
    const columnsCount = getColumnsCount() // 修复：使用函数获取列数，而不是直接访问变量
    const cellSize = Math.floor(gridRect.width / columnsCount)

    // 估算网格坐标
    const gridX = Math.round(relativeX / cellSize)
    const gridY = Math.round(relativeY / cellSize)

    appPositions.push({
      id,
      x: gridX,
      y: gridY
    })
  })

  // 更新应用的位置信息
  const apps = categoryApps.value[activeCategory.value]
  appPositions.forEach(pos => {
    const index = apps.findIndex(app => app.id === pos.id)
    if (index !== -1) {
      apps[index].x = pos.x.toString()
      apps[index].y = pos.y.toString()
    }
  })

  // 保存更新
  saveAppOrder()
}

// 确保拖拽功能已正确初始化
function ensureSortableInitialized() {
  try {
    // 检查Sortable实例是否存在
    if (!sortableInstance) {
      // 立即执行初始化
      initSortable();
      return;
    }

    // 安全地检查option方法是否存在
    if (typeof sortableInstance.option !== 'function') {
      console.warn('Sortable实例已损坏，重新初始化');
      // 尝试销毁当前实例并重新创建
      try {
        sortableInstance.destroy();
      } catch (e) {
        console.warn('销毁损坏的Sortable实例失败:', e);
      }
      sortableInstance = null;
      initSortable();
      return;
    }

    // 如果实例已禁用，启用它
    try {
      if (sortableInstance.option('disabled')) {
        sortableInstance.option('disabled', false);
      }

      // 优化选项以提高流畅度
      sortableInstance.option('animation', 200);
      sortableInstance.option('delay', 0);
      sortableInstance.option('delayOnTouchOnly', false);
    } catch (error) {
      console.warn('设置Sortable选项失败，重新初始化:', error);
      // 重新初始化
      sortableInstance = null;
      initSortable();
    }
  } catch (error) {
    console.error('确保Sortable初始化时出错:', error);
    // 出现任何错误都重新初始化
    sortableInstance = null;
    initSortable();
  }
}

// 文件夹应用弹窗
const folderModalVisible = ref(false)
const activeFolder = ref(null)



// 打开文件夹应用弹窗
function openFolderModal(folder) {
  folderModalVisible.value = true
  activeFolder.value = folder
}

// 关闭文件夹应用弹窗
function closeFolderModal() {
  folderModalVisible.value = false
  activeFolder.value = null
}

// 从文件夹应用中打开应用
function openAppFromFolder(app) {
  openApp(app)
  // 不关闭弹窗，让用户继续浏览
}

// 处理文件夹标题更新
function handleUpdateFolderTitle(newTitle) {
  if (!activeFolder.value || !newTitle.trim()) {
    return
  }

  // 更新当前活动文件夹的名称
  activeFolder.value.name = newTitle.trim()

  // 查找并更新存储中的文件夹数据
  const currentApps = getCurrentCategoryApps()
  const folderIndex = currentApps.findIndex(app => app.id === activeFolder.value.id)

  if (folderIndex !== -1) {
    currentApps[folderIndex].name = newTitle.trim()

    // 保存到localStorage
    saveAppOrder()

    console.log(`文件夹标题已更新为: ${newTitle.trim()}`)
  }
}

// 处理添加应用到文件夹
function handleAddAppToFolder(app) {
  if (!activeFolder.value) return;

  // 确保文件夹有children数组
  if (!activeFolder.value.children) {
    activeFolder.value.children = [];
  }

  // 检查应用是否已存在于文件夹中
  const existingApp = activeFolder.value.children.find(child => child.name === app.name);
  if (existingApp) {
    message.warning(`${app.name} 已存在于文件夹中`);
    return;
  }

  // 添加应用到文件夹
  activeFolder.value.children.push(app);

  // 更新主应用列表中的文件夹数据
  const folderInAllApps = allApps.value.find(item => item.id === activeFolder.value.id);
  if (folderInAllApps) {
    folderInAllApps.children = [...activeFolder.value.children];
  }

  // 更新分类应用列表中的文件夹数据
  const folderInCategory = categoryApps.value[activeCategory.value]?.find(item => item.id === activeFolder.value.id);
  if (folderInCategory) {
    folderInCategory.children = [...activeFolder.value.children];
  }

  // 同步到 navigation store
  syncToNavigationStore(activeCategory.value, categoryApps.value[activeCategory.value]);

  // 保存更新
  saveAppOrder();

  // 触发自动备份
  autoBackupManager.triggerAutoBackup();

  message.success(`已添加 ${app.name} 到文件夹`);
}

// 处理从文件夹中移除应用
function handleRemoveAppFromFolder(app) {
  if (!activeFolder.value) return;

  // 更新主应用列表中的文件夹数据
  const folderInAllApps = allApps.value.find(item => item.id === activeFolder.value.id);
  if (folderInAllApps) {
    folderInAllApps.children = [...activeFolder.value.children];
  }

  // 更新分类应用列表中的文件夹数据
  const folderInCategory = categoryApps.value[activeCategory.value]?.find(item => item.id === activeFolder.value.id);
  if (folderInCategory) {
    folderInCategory.children = [...activeFolder.value.children];
  }

  // 同步到 navigation store
  syncToNavigationStore(activeCategory.value, categoryApps.value[activeCategory.value]);

  // 保存更新
  saveAppOrder();

  // 触发自动备份
  autoBackupManager.triggerAutoBackup();
}

// 处理将应用从文件夹添加到桌面
function handleAddAppToDesktop(app) {
  if (!activeFolder.value) return;

  // 将应用添加到当前分类
  const currentApps = categoryApps.value[activeCategory.value] || [];

  // 检查应用是否已存在于当前分类中
  const existingApp = currentApps.find(existingApp => existingApp.id === app.id);
  if (existingApp) {
    message.warning(`${app.name} 已存在于当前分类中`);
    return;
  }

  // 添加应用到当前分类
  currentApps.push({
    ...app,
    category: activeCategory.value,
    x: 0,
    y: 0
  });

  // 更新主应用列表中的文件夹数据
  const folderInAllApps = allApps.value.find(item => item.id === activeFolder.value.id);
  if (folderInAllApps) {
    folderInAllApps.children = [...activeFolder.value.children];
  }

  // 更新分类应用列表中的文件夹数据
  const folderInCategory = categoryApps.value[activeCategory.value]?.find(item => item.id === activeFolder.value.id);
  if (folderInCategory) {
    folderInCategory.children = [...activeFolder.value.children];
  }

  // 同步到 navigation store
  syncToNavigationStore(activeCategory.value, categoryApps.value[activeCategory.value]);

  // 保存更新
  saveAppOrder();

  // 触发自动备份
  autoBackupManager.triggerAutoBackup();
}

// 在初始化Sortable后调用
function initDragAndDrop() {
  makeAppItemsDraggable();
}




// 创建特殊卡片的永久备份，不会随分类切换而变化
const specialCardsMaster = ref([...allApps.value.filter(app =>
  app.type === 'iframe' || app.type === 'weather' ||
  app.type === 'clock' || app.type === 'woodenFish' || app.type === 'imageCard' ||
  app.type === 'linkCard' || app.type === 'video' || app.type === 'RelaxCard' || app.type === 'HotCard' || app.type === 'ImageCard'
)])

// 应用合集弹窗相关状态
const collectionModalVisible = ref(false)
const activeCollection = ref(null)

// 打开应用合集弹窗
function openCollectionModal(collection) {

  // 深拷贝集合确保不修改原始数据
  const collectionCopy = JSON.parse(JSON.stringify(collection));
  // 确保children属性存在且格式正确
  if (collectionCopy.children && collectionCopy.children.length > 0) {

    // 处理子项，确保每个子项具有正确的属性
    collectionCopy.children = collectionCopy.children.map(child => {
      // 如果使用了logo而不是icon，进行转换
      if (child.logo && !child.icon) {
        child.icon = child.logo;
      }

      // 确保URL格式正确
      if (child.websiteAddress && !child.url) {
        child.url = `https://${child.websiteAddress}`;
      }

      return child;
    });
  } else {

    // 尝试从原始对象中再次检查
    if (collection.children && collection.children.length > 0) {
      collectionCopy.children = JSON.parse(JSON.stringify(collection.children));
    } else {
      // 确保存在空的children数组
      collectionCopy.children = [];
    }
  }
  collectionModalVisible.value = true;
  activeCollection.value = collectionCopy;
}

// 关闭应用合集弹窗
function closeCollectionModal() {
  collectionModalVisible.value = false
  activeCollection.value = null
}

// 从应用合集中打开应用
function openAppFromCollection(app) {

  // 修复应用数据格式问题
  if (!app.icon && app.logo) {
    app.icon = app.logo;
  }

  // 修复URL问题
  if (app.websiteAddress && !app.url) {
    app.url = `https://${app.websiteAddress}`;
  }

  // 先关闭合集弹窗，然后再打开应用弹窗
  // collectionModalVisible.value = false;

  // 延迟一下再打开应用，确保合集弹窗已完全关闭
  setTimeout(() => {
    openApp(app);
  }, 100);
}


// 办公模式切换按钮 - 优化
const showOfficeSwitcher = ref(false)


// 添加一个函数来正确拼接URL
const concatUrl = (path) => {
  // 如果path为null那么就随机a-z加上.png
  return urlStore.concatUrl(path)
}



// 添加标志位，用于标记是否正在修改应用尺寸或显示右键菜单
const isChangingAppSize = ref(false)
const isContextMenuActive = ref(false)









/**
 * 初始化简化的滑动切换功能
 */
function initSwipeNavigation() {
  // 清理旧的事件监听器
  if (window._globalWheelHandler) {
    window.removeEventListener('wheel', window._globalWheelHandler, { capture: true });
    delete window._globalWheelHandler;
  }
  if (window._globalTouchMoveHandler) {
    window.removeEventListener('touchmove', window._globalTouchMoveHandler, { capture: true });
    delete window._globalTouchMoveHandler;
  }
  if (window._globalTouchEndHandler) {
    window.removeEventListener('touchend', window._globalTouchEndHandler, { capture: true });
    delete window._globalTouchEndHandler;
  }

  // 使用较短的延迟确保DOM已渲染
  setTimeout(() => {
    // 获取应用网格区域
    const appsSection = document.querySelector('.app-grid');

    if (appsSection) {
      // 创建全局滚轮事件处理器
      window._globalWheelHandler = function (e) {
        // 检查滚动速度限制
        const currentTime = Date.now();
        if (currentTime - lastScrollTime.value < SCROLL_COOLDOWN_TIME) {
          // 在冷却期内，忽略滚动
          return;
        }

        // 如果正在切换分类或在冷却期，忽略滚动
        if (isChangingCategory.value || wheelCooldown.value) {
          return;
        }

        // 检查是否在需要排除的组件内滚动
        const shouldExclude = e.target.closest(
          // 弹窗和模态框
          '.app-modal-overlay, .app-modal, .hotnet-modal, .modal-list, .settings-modal, .wallpaper-modal, .settings-modal-overlay' +
          // 抽屉和侧边栏
          '.drawer-container, .drawer-content, .mobile-drawer, ' +
          // 有滚动条的组件
          '.hotnet-container, .video-card, [class*="card-component"], ' +
          '.danmaku-container, .tasks-container, .all-platforms-sidebar, ' +
          // Ant Design 组件
          '.ant-modal, .ant-drawer, .ant-select-dropdown, .ant-tooltip, ' +
          '.ant-popover, .ant-dropdown, .ant-menu, ' +
          // 其他可滚动容器
          '[data-scrollable], .scrollable, .overflow-auto, .overflow-y-auto'
        );

        if (shouldExclude) {
          // 在排除的组件内，不触发分类切换
          return;
        }

        // 检查滚动事件是否发生在应用网格区域内 - 使用closest方法更准确地检测
        const isInsideAppsSection = e.target.closest('.app-grid, .app-item') !== null;

        if (!isInsideAppsSection) {
          // 在应用网格外且不在排除组件内的滚动才触发分类切换
          e.preventDefault();

          // 更新最后滚动时间
          lastScrollTime.value = currentTime;

          if (e.deltaY > 0) {
            // 向下滚动，切换到下一分类
            switchToNextCategory();
          } else if (e.deltaY < 0) {
            // 向上滚动，切换到上一分类
            switchToPreviousCategory();
          }
        }
        // 如果在应用网格内或排除组件内，让默认滚动行为处理
      };

      // 创建全局触摸事件处理器
      window._globalTouchMoveHandler = function (e) {
        handleTouchMove(e);
      };

      window._globalTouchEndHandler = function (e) {
        handleTouchEnd(e);
      };

      // 添加全局事件监听器
      window.addEventListener('wheel', window._globalWheelHandler, {
        capture: true,
        passive: false
      });
      window.addEventListener('touchmove', window._globalTouchMoveHandler, {
        capture: true,
        passive: true
      });
      window.addEventListener('touchend', window._globalTouchEndHandler, {
        capture: true,
        passive: true
      });
    }
  }, 100);
}

// 触摸事件起点
let touchStartY = 0;
let touchStartX = 0;






// 获取可用的网格模式
function getAvailableGridModes() {
  return defaultCalculator.getAvailablePresets()
}

// 处理布局设置变更事件
function handleLayoutSettingChanged() {
  // 重新计算最佳布局参数
  calculateOptimalGridParams();

  // 更新网格样式
  nextTick(() => {
    // 重新初始化Sortable以应用新的列数
    initSortable()
  })
}





</script>
<style>

</style>
<style lang="scss" scoped>
@import '../views/home.scss';
/* 添加防止文本选择的样式 */
.home-container {
  user-select: none;
  /* 防止双击选中文本和图标 */
}

.getLayoutInfo {
  position: absolute;
  bottom: 0px;
  right: 0px;
  /* background: black; */
  // font-size: 12px;
  color: white;
  background: none;
  border: none;
  outline: none;
  a{
    color: white;
    font-size: 10px;
  }
}

.backupInfo,
.restoreBackup {
  position: absolute;
  bottom: 60px;
  right: 10px;
  background: black;
  color: white;
}

.restoreBackup {
  bottom: 120px;
  z-index: 999999999;
}



.data-source-text {
  display: flex;
  align-items: center;
  gap: 6px;
}

.data-source-text::before {
  content: '🔄';
  font-size: 16px;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }

  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 数据源切换动画 */
.fade-out {
  -webkit-animation: fade-out 0.6s cubic-bezier(.39, .575, .565, 1.000) both;
  animation: fade-out 0.6s cubic-bezier(.39, .575, .565, 1.000) both;
}

.fade-in {
  -webkit-animation: fade-in 1.2s cubic-bezier(.39, .575, .565, 1.000) both;
  animation: fade-in 1.2s cubic-bezier(.39, .575, .565, 1.000) both;
}

@-webkit-keyframes fade-out {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

@keyframes fade-out {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

@-webkit-keyframes fade-in {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes fade-in {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

/* 简单加载指示器样式 */
.simple-loading-indicator {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 3000;
  pointer-events: none;
}

.loading-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border-radius: 20px;
  padding: 16px 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 160px;
}

.loading-text {
  font-size: 14px;
  color: #666;
  font-weight: 500;
  text-align: center;
}

/* GSAP响应式优化 */
@media (max-width: 768px) {
  .loading-card {
    padding: 20px 28px;
    min-width: 180px;
  }

  .loading-icon {
    font-size: 28px;
  }

  .loading-text {
    font-size: 13px;
  }
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  .gsap-card-container {
    transition: none;
  }

  .loading-icon {
    animation: none;
  }
}

/* 添加图标应用样式 */
.add-icon-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  cursor: pointer;
  border-radius: 15px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  .app-name{
    position: absolute;
    bottom: -20px;
  
  }
}


.add-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  border-radius: 12px;
}



.add-icon-card:hover .add-icon-plus {
  color: #495057;
  transform: scale(1.1);
}

.add-icon-card .app-name {
  margin-top: 8px;
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
  text-align: center;
  transition: color 0.3s ease;
}

.add-icon-card:hover .app-name {
  color: #495057;
}

/* 编辑模式下的添加图标样式 */
.add-icon-card[style*="animation"] {
  border-style: solid;
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border-color: #ffc107;
}

/* 添加图标应用项的特殊样式 */
.add-icon-item {
  cursor: pointer !important;
  user-select: none;
}

.add-icon-item:hover {
  transform: translateY(-2px);
  transition: transform 0.3s ease;
}

/* 禁用添加图标应用的拖拽样式 */
.add-icon-item[draggable="false"] {
  cursor: pointer !important;
}

.add-icon-item .delete-icon {
  display: none !important;
}
/* 纯净模式动画效果 - 全局样式 */
:deep(.pure-mode-animation) {
  transform: translateY(200px) !important;
  // transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1) !important;
}
:deep(.search-section.pure-mode-animation) {
  transform: translateY(200px) !important;
  // transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1) !important;
}
/* 默认状态 - 为所有相关组件添加过渡效果 */
:deep(.time-display),
:deep(.search-section) {
  // transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0);
}
</style>